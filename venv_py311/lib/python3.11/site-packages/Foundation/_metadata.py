# This file is generated by objective.metadata
#
# Last update: Sun Nov 17 12:08:28 2024
#
# flake8: noqa

import objc, sys
from typing import NewType

if sys.maxsize > 2**32:

    def sel32or64(a, b):
        return b

else:

    def sel32or64(a, b):
        return a


if objc.arch == "arm64":

    def selAorI(a, b):
        return a

else:

    def selAorI(a, b):
        return b


misc = {}
misc.update(
    {
        "NSEdgeInsets": objc.createStructType(
            "Foundation.NSEdgeInsets",
            b"{NSEdgeInsets=dddd}",
            ["top", "left", "bottom", "right"],
        ),
        "NSHashEnumerator": objc.createStructType(
            "Foundation.NSHashEnumerator",
            b"{NSHashEnumerator=QQ^v}",
            ["_pi", "_si", "_bs"],
        ),
        "NSAffineTransformStruct": objc.createStructType(
            "Foundation.NSAffineTransformStruct",
            b"{NSAffineTransformStruct=dddddd}",
            ["m11", "m12", "m21", "m22", "tX", "tY"],
        ),
        "NSOperatingSystemVersion": objc.createStructType(
            "Foundation.NSOperatingSystemVersion",
            b"{NSOperatingSystemVersion=qqq}",
            ["majorVersion", "minorVersion", "patchVersion"],
        ),
        "NSZone": objc.createStructType("Foundation.NSZone", b"{_NSZone=}", []),
        "NSDecimal": objc.createStructType(
            "Foundation.NSDecimal",
            b"{NSDecimal=b8b4b1b1b18[8S]}",
            [
                "_exponent",
                "_length",
                "_isNegative",
                "_isCompact",
                "_reserved",
                "_mantissa",
            ],
        ),
        "NSSwappedDouble": objc.createStructType(
            "Foundation.NSSwappedDouble", b"{NSSwappedDouble=Q}", ["v"]
        ),
        "NSMapEnumerator": objc.createStructType(
            "Foundation.NSMapEnumerator",
            b"{NSMapEnumerator=QQ^v}",
            ["_pi", "_si", "_bs"],
        ),
        "NSSwappedFloat": objc.createStructType(
            "Foundation.NSSwappedFloat", b"{NSSwappedFloat=I}", ["v"]
        ),
        "NSRange": objc.createStructType(
            "Foundation.NSRange", b"{_NSRange=QQ}", ["location", "length"]
        ),
    }
)
constants = """$NSAMPMDesignation$NSAlternateDescriptionAttributeName$NSAppleEventManagerWillProcessFirstEventNotification$NSAppleEventTimeOutDefault@d$NSAppleEventTimeOutNone@d$NSAppleScriptErrorAppName$NSAppleScriptErrorBriefMessage$NSAppleScriptErrorMessage$NSAppleScriptErrorNumber$NSAppleScriptErrorRange$NSArgumentDomain$NSAssertionHandlerKey$NSAverageKeyValueOperator$NSBuddhistCalendar$NSBundleDidLoadNotification$NSBundleResourceRequestLoadingPriorityUrgent@d$NSBundleResourceRequestLowDiskSpaceNotification$NSCalendarDayChangedNotification$NSCalendarIdentifierBuddhist$NSCalendarIdentifierChinese$NSCalendarIdentifierCoptic$NSCalendarIdentifierEthiopicAmeteAlem$NSCalendarIdentifierEthiopicAmeteMihret$NSCalendarIdentifierGregorian$NSCalendarIdentifierHebrew$NSCalendarIdentifierISO8601$NSCalendarIdentifierIndian$NSCalendarIdentifierIslamic$NSCalendarIdentifierIslamicCivil$NSCalendarIdentifierIslamicTabular$NSCalendarIdentifierIslamicUmmAlQura$NSCalendarIdentifierJapanese$NSCalendarIdentifierPersian$NSCalendarIdentifierRepublicOfChina$NSCharacterConversionException$NSChineseCalendar$NSClassDescriptionNeededForClassNotification$NSCocoaErrorDomain$NSConnectionDidDieNotification$NSConnectionDidInitializeNotification$NSConnectionReplyMode$NSCountKeyValueOperator$NSCurrencySymbol$NSCurrentLocaleDidChangeNotification$NSDateFormatString$NSDateTimeOrdering$NSDeallocateZombies@Z$NSDebugDescriptionErrorKey$NSDebugEnabled@Z$NSDecimalDigits$NSDecimalNumberDivideByZeroException$NSDecimalNumberExactnessException$NSDecimalNumberOverflowException$NSDecimalNumberUnderflowException$NSDecimalSeparator$NSDefaultRunLoopMode$NSDestinationInvalidException$NSDidBecomeSingleThreadedNotification$NSDistinctUnionOfArraysKeyValueOperator$NSDistinctUnionOfObjectsKeyValueOperator$NSDistinctUnionOfSetsKeyValueOperator$NSEarlierTimeDesignations$NSEdgeInsetsZero@{NSEdgeInsets=dddd}$NSErrorFailingURLStringKey$NSExtensionHostDidBecomeActiveNotification$NSExtensionHostDidEnterBackgroundNotification$NSExtensionHostWillEnterForegroundNotification$NSExtensionHostWillResignActiveNotification$NSExtensionItemAttachmentsKey$NSExtensionItemAttributedContentTextKey$NSExtensionItemAttributedTitleKey$NSExtensionItemsAndErrorsKey$NSExtensionJavaScriptFinalizeArgumentKey$NSExtensionJavaScriptPreprocessingResultsKey$NSFTPPropertyActiveTransferModeKey$NSFTPPropertyFTPProxy$NSFTPPropertyFileOffsetKey$NSFTPPropertyUserLoginKey$NSFTPPropertyUserPasswordKey$NSFailedAuthenticationException$NSFileAppendOnly$NSFileBusy$NSFileCreationDate$NSFileDeviceIdentifier$NSFileExtensionHidden$NSFileGroupOwnerAccountID$NSFileGroupOwnerAccountName$NSFileHFSCreatorCode$NSFileHFSTypeCode$NSFileHandleConnectionAcceptedNotification$NSFileHandleDataAvailableNotification$NSFileHandleNotificationDataItem$NSFileHandleNotificationFileHandleItem$NSFileHandleNotificationMonitorModes$NSFileHandleOperationException$NSFileHandleReadCompletionNotification$NSFileHandleReadToEndOfFileCompletionNotification$NSFileImmutable$NSFileManagerUnmountDissentingProcessIdentifierErrorKey$NSFileModificationDate$NSFileOwnerAccountID$NSFileOwnerAccountName$NSFilePathErrorKey$NSFilePosixPermissions$NSFileProtectionComplete$NSFileProtectionCompleteUnlessOpen$NSFileProtectionCompleteUntilFirstUserAuthentication$NSFileProtectionCompleteWhenUserInactive$NSFileProtectionKey$NSFileProtectionNone$NSFileReferenceCount$NSFileSize$NSFileSystemFileNumber$NSFileSystemFreeNodes$NSFileSystemFreeSize$NSFileSystemNodes$NSFileSystemNumber$NSFileSystemSize$NSFileType$NSFileTypeBlockSpecial$NSFileTypeCharacterSpecial$NSFileTypeDirectory$NSFileTypeRegular$NSFileTypeSocket$NSFileTypeSymbolicLink$NSFileTypeUnknown$NSFoundationVersionNumber@d$NSGenericException$NSGlobalDomain$NSGrammarCorrections$NSGrammarRange$NSGrammarUserDescription$NSGregorianCalendar$NSHTTPCookieComment$NSHTTPCookieCommentURL$NSHTTPCookieDiscard$NSHTTPCookieDomain$NSHTTPCookieExpires$NSHTTPCookieManagerAcceptPolicyChangedNotification$NSHTTPCookieManagerCookiesChangedNotification$NSHTTPCookieMaximumAge$NSHTTPCookieName$NSHTTPCookieOriginURL$NSHTTPCookiePath$NSHTTPCookiePort$NSHTTPCookieSameSiteLax$NSHTTPCookieSameSitePolicy$NSHTTPCookieSameSiteStrict$NSHTTPCookieSecure$NSHTTPCookieSetByJavaScript$NSHTTPCookieValue$NSHTTPCookieVersion$NSHTTPPropertyErrorPageDataKey$NSHTTPPropertyHTTPProxy$NSHTTPPropertyRedirectionHeadersKey$NSHTTPPropertyServerHTTPVersionKey$NSHTTPPropertyStatusCodeKey$NSHTTPPropertyStatusReasonKey$NSHangOnUncaughtException@Z$NSHebrewCalendar$NSHelpAnchorErrorKey$NSHourNameDesignations$NSISO8601Calendar$NSImageURLAttributeName$NSInconsistentArchiveException$NSIndianCalendar$NSInflectionAgreementArgumentAttributeName$NSInflectionAgreementConceptAttributeName$NSInflectionAlternativeAttributeName$NSInflectionConceptsKey$NSInflectionReferentConceptAttributeName$NSInflectionRuleAttributeName$NSInlinePresentationIntentAttributeName$NSInternalInconsistencyException$NSInternationalCurrencyString$NSInvalidArchiveOperationException$NSInvalidArgumentException$NSInvalidReceivePortException$NSInvalidSendPortException$NSInvalidUnarchiveOperationException$NSInvocationOperationCancelledException$NSInvocationOperationVoidResultException$NSIsNilTransformerName$NSIsNotNilTransformerName$NSIslamicCalendar$NSIslamicCivilCalendar$NSItemProviderErrorDomain$NSItemProviderPreferredImageSizeKey$NSJapaneseCalendar$NSKeepAllocationStatistics@Z$NSKeyValueChangeIndexesKey$NSKeyValueChangeKindKey$NSKeyValueChangeNewKey$NSKeyValueChangeNotificationIsPriorKey$NSKeyValueChangeOldKey$NSKeyedArchiveRootObjectKey$NSKeyedUnarchiveFromDataTransformerName$NSLanguageIdentifierAttributeName$NSLaterTimeDesignations$NSLinguisticTagAdjective$NSLinguisticTagAdverb$NSLinguisticTagClassifier$NSLinguisticTagCloseParenthesis$NSLinguisticTagCloseQuote$NSLinguisticTagConjunction$NSLinguisticTagDash$NSLinguisticTagDeterminer$NSLinguisticTagIdiom$NSLinguisticTagInterjection$NSLinguisticTagNoun$NSLinguisticTagNumber$NSLinguisticTagOpenParenthesis$NSLinguisticTagOpenQuote$NSLinguisticTagOrganizationName$NSLinguisticTagOther$NSLinguisticTagOtherPunctuation$NSLinguisticTagOtherWhitespace$NSLinguisticTagOtherWord$NSLinguisticTagParagraphBreak$NSLinguisticTagParticle$NSLinguisticTagPersonalName$NSLinguisticTagPlaceName$NSLinguisticTagPreposition$NSLinguisticTagPronoun$NSLinguisticTagPunctuation$NSLinguisticTagSchemeLanguage$NSLinguisticTagSchemeLemma$NSLinguisticTagSchemeLexicalClass$NSLinguisticTagSchemeNameType$NSLinguisticTagSchemeNameTypeOrLexicalClass$NSLinguisticTagSchemeScript$NSLinguisticTagSchemeTokenType$NSLinguisticTagSentenceTerminator$NSLinguisticTagVerb$NSLinguisticTagWhitespace$NSLinguisticTagWord$NSLinguisticTagWordJoiner$NSLoadedClasses$NSLocalNotificationCenterType$NSLocaleAlternateQuotationBeginDelimiterKey$NSLocaleAlternateQuotationEndDelimiterKey$NSLocaleCalendar$NSLocaleCollationIdentifier$NSLocaleCollatorIdentifier$NSLocaleCountryCode$NSLocaleCurrencyCode$NSLocaleCurrencySymbol$NSLocaleDecimalSeparator$NSLocaleExemplarCharacterSet$NSLocaleGroupingSeparator$NSLocaleIdentifier$NSLocaleLanguageCode$NSLocaleMeasurementSystem$NSLocaleQuotationBeginDelimiterKey$NSLocaleQuotationEndDelimiterKey$NSLocaleScriptCode$NSLocaleUsesMetricSystem$NSLocaleVariantCode$NSLocalizedDescriptionKey$NSLocalizedFailureErrorKey$NSLocalizedFailureReasonErrorKey$NSLocalizedNumberFormatAttributeName$NSLocalizedRecoveryOptionsErrorKey$NSLocalizedRecoverySuggestionErrorKey$NSMachErrorDomain$NSMallocException$NSMarkdownSourcePositionAttributeName$NSMaximumKeyValueOperator$NSMetadataItemAcquisitionMakeKey$NSMetadataItemAcquisitionModelKey$NSMetadataItemAlbumKey$NSMetadataItemAltitudeKey$NSMetadataItemApertureKey$NSMetadataItemAppleLoopDescriptorsKey$NSMetadataItemAppleLoopsKeyFilterTypeKey$NSMetadataItemAppleLoopsLoopModeKey$NSMetadataItemAppleLoopsRootKeyKey$NSMetadataItemApplicationCategoriesKey$NSMetadataItemAttributeChangeDateKey$NSMetadataItemAudiencesKey$NSMetadataItemAudioBitRateKey$NSMetadataItemAudioChannelCountKey$NSMetadataItemAudioEncodingApplicationKey$NSMetadataItemAudioSampleRateKey$NSMetadataItemAudioTrackNumberKey$NSMetadataItemAuthorAddressesKey$NSMetadataItemAuthorEmailAddressesKey$NSMetadataItemAuthorsKey$NSMetadataItemBitsPerSampleKey$NSMetadataItemCFBundleIdentifierKey$NSMetadataItemCameraOwnerKey$NSMetadataItemCityKey$NSMetadataItemCodecsKey$NSMetadataItemColorSpaceKey$NSMetadataItemCommentKey$NSMetadataItemComposerKey$NSMetadataItemContactKeywordsKey$NSMetadataItemContentCreationDateKey$NSMetadataItemContentModificationDateKey$NSMetadataItemContentTypeKey$NSMetadataItemContentTypeTreeKey$NSMetadataItemContributorsKey$NSMetadataItemCopyrightKey$NSMetadataItemCountryKey$NSMetadataItemCoverageKey$NSMetadataItemCreatorKey$NSMetadataItemDateAddedKey$NSMetadataItemDeliveryTypeKey$NSMetadataItemDescriptionKey$NSMetadataItemDirectorKey$NSMetadataItemDisplayNameKey$NSMetadataItemDownloadedDateKey$NSMetadataItemDueDateKey$NSMetadataItemDurationSecondsKey$NSMetadataItemEXIFGPSVersionKey$NSMetadataItemEXIFVersionKey$NSMetadataItemEditorsKey$NSMetadataItemEmailAddressesKey$NSMetadataItemEncodingApplicationsKey$NSMetadataItemExecutableArchitecturesKey$NSMetadataItemExecutablePlatformKey$NSMetadataItemExposureModeKey$NSMetadataItemExposureProgramKey$NSMetadataItemExposureTimeSecondsKey$NSMetadataItemExposureTimeStringKey$NSMetadataItemFNumberKey$NSMetadataItemFSContentChangeDateKey$NSMetadataItemFSCreationDateKey$NSMetadataItemFSNameKey$NSMetadataItemFSSizeKey$NSMetadataItemFinderCommentKey$NSMetadataItemFlashOnOffKey$NSMetadataItemFocalLength35mmKey$NSMetadataItemFocalLengthKey$NSMetadataItemFontsKey$NSMetadataItemGPSAreaInformationKey$NSMetadataItemGPSDOPKey$NSMetadataItemGPSDateStampKey$NSMetadataItemGPSDestBearingKey$NSMetadataItemGPSDestDistanceKey$NSMetadataItemGPSDestLatitudeKey$NSMetadataItemGPSDestLongitudeKey$NSMetadataItemGPSDifferentalKey$NSMetadataItemGPSMapDatumKey$NSMetadataItemGPSMeasureModeKey$NSMetadataItemGPSProcessingMethodKey$NSMetadataItemGPSStatusKey$NSMetadataItemGPSTrackKey$NSMetadataItemGenreKey$NSMetadataItemHasAlphaChannelKey$NSMetadataItemHeadlineKey$NSMetadataItemISOSpeedKey$NSMetadataItemIdentifierKey$NSMetadataItemImageDirectionKey$NSMetadataItemInformationKey$NSMetadataItemInstantMessageAddressesKey$NSMetadataItemInstructionsKey$NSMetadataItemIsApplicationManagedKey$NSMetadataItemIsGeneralMIDISequenceKey$NSMetadataItemIsLikelyJunkKey$NSMetadataItemIsUbiquitousKey$NSMetadataItemKeySignatureKey$NSMetadataItemKeywordsKey$NSMetadataItemKindKey$NSMetadataItemLanguagesKey$NSMetadataItemLastUsedDateKey$NSMetadataItemLatitudeKey$NSMetadataItemLayerNamesKey$NSMetadataItemLensModelKey$NSMetadataItemLongitudeKey$NSMetadataItemLyricistKey$NSMetadataItemMaxApertureKey$NSMetadataItemMediaTypesKey$NSMetadataItemMeteringModeKey$NSMetadataItemMusicalGenreKey$NSMetadataItemMusicalInstrumentCategoryKey$NSMetadataItemMusicalInstrumentNameKey$NSMetadataItemNamedLocationKey$NSMetadataItemNumberOfPagesKey$NSMetadataItemOrganizationsKey$NSMetadataItemOrientationKey$NSMetadataItemOriginalFormatKey$NSMetadataItemOriginalSourceKey$NSMetadataItemPageHeightKey$NSMetadataItemPageWidthKey$NSMetadataItemParticipantsKey$NSMetadataItemPathKey$NSMetadataItemPerformersKey$NSMetadataItemPhoneNumbersKey$NSMetadataItemPixelCountKey$NSMetadataItemPixelHeightKey$NSMetadataItemPixelWidthKey$NSMetadataItemProducerKey$NSMetadataItemProfileNameKey$NSMetadataItemProjectsKey$NSMetadataItemPublishersKey$NSMetadataItemRecipientAddressesKey$NSMetadataItemRecipientEmailAddressesKey$NSMetadataItemRecipientsKey$NSMetadataItemRecordingDateKey$NSMetadataItemRecordingYearKey$NSMetadataItemRedEyeOnOffKey$NSMetadataItemResolutionHeightDPIKey$NSMetadataItemResolutionWidthDPIKey$NSMetadataItemRightsKey$NSMetadataItemSecurityMethodKey$NSMetadataItemSpeedKey$NSMetadataItemStarRatingKey$NSMetadataItemStateOrProvinceKey$NSMetadataItemStreamableKey$NSMetadataItemSubjectKey$NSMetadataItemTempoKey$NSMetadataItemTextContentKey$NSMetadataItemThemeKey$NSMetadataItemTimeSignatureKey$NSMetadataItemTimestampKey$NSMetadataItemTitleKey$NSMetadataItemTotalBitRateKey$NSMetadataItemURLKey$NSMetadataItemVersionKey$NSMetadataItemVideoBitRateKey$NSMetadataItemWhereFromsKey$NSMetadataItemWhiteBalanceKey$NSMetadataQueryAccessibleUbiquitousExternalDocumentsScope$NSMetadataQueryDidFinishGatheringNotification$NSMetadataQueryDidStartGatheringNotification$NSMetadataQueryDidUpdateNotification$NSMetadataQueryGatheringProgressNotification$NSMetadataQueryIndexedLocalComputerScope$NSMetadataQueryIndexedNetworkScope$NSMetadataQueryLocalComputerScope$NSMetadataQueryLocalDocumentsScope$NSMetadataQueryNetworkScope$NSMetadataQueryResultContentRelevanceAttribute$NSMetadataQueryUbiquitousDataScope$NSMetadataQueryUbiquitousDocumentsScope$NSMetadataQueryUpdateAddedItemsKey$NSMetadataQueryUpdateChangedItemsKey$NSMetadataQueryUpdateRemovedItemsKey$NSMetadataQueryUserHomeScope$NSMetadataUbiquitousItemContainerDisplayNameKey$NSMetadataUbiquitousItemDownloadRequestedKey$NSMetadataUbiquitousItemDownloadingErrorKey$NSMetadataUbiquitousItemDownloadingStatusCurrent$NSMetadataUbiquitousItemDownloadingStatusDownloaded$NSMetadataUbiquitousItemDownloadingStatusKey$NSMetadataUbiquitousItemDownloadingStatusNotDownloaded$NSMetadataUbiquitousItemHasUnresolvedConflictsKey$NSMetadataUbiquitousItemIsDownloadedKey$NSMetadataUbiquitousItemIsDownloadingKey$NSMetadataUbiquitousItemIsExternalDocumentKey$NSMetadataUbiquitousItemIsSharedKey$NSMetadataUbiquitousItemIsUploadedKey$NSMetadataUbiquitousItemIsUploadingKey$NSMetadataUbiquitousItemPercentDownloadedKey$NSMetadataUbiquitousItemPercentUploadedKey$NSMetadataUbiquitousItemURLInLocalContainerKey$NSMetadataUbiquitousItemUploadingErrorKey$NSMetadataUbiquitousSharedItemCurrentUserPermissionsKey$NSMetadataUbiquitousSharedItemCurrentUserRoleKey$NSMetadataUbiquitousSharedItemMostRecentEditorNameComponentsKey$NSMetadataUbiquitousSharedItemOwnerNameComponentsKey$NSMetadataUbiquitousSharedItemPermissionsReadOnly$NSMetadataUbiquitousSharedItemPermissionsReadWrite$NSMetadataUbiquitousSharedItemRoleOwner$NSMetadataUbiquitousSharedItemRoleParticipant$NSMinimumKeyValueOperator$NSMonthNameArray$NSMorphologyAttributeName$NSMultipleUnderlyingErrorsKey$NSNegateBooleanTransformerName$NSNegativeCurrencyFormatString$NSNetServicesErrorCode$NSNetServicesErrorDomain$NSNextDayDesignations$NSNextNextDayDesignations$NSOSStatusErrorDomain$NSObjectInaccessibleException$NSObjectNotAvailableException$NSOldStyleException$NSOperationNotSupportedForKeyException$NSPOSIXErrorDomain$NSParseErrorException$NSPersianCalendar$NSPersonNameComponentDelimiter$NSPersonNameComponentFamilyName$NSPersonNameComponentGivenName$NSPersonNameComponentKey$NSPersonNameComponentMiddleName$NSPersonNameComponentNickname$NSPersonNameComponentPrefix$NSPersonNameComponentSuffix$NSPortDidBecomeInvalidNotification$NSPortReceiveException$NSPortSendException$NSPortTimeoutException$NSPositiveCurrencyFormatString$NSPresentationIntentAttributeName$NSPriorDayDesignations$NSProcessInfoPowerStateDidChangeNotification$NSProcessInfoThermalStateDidChangeNotification$NSProgressEstimatedTimeRemainingKey$NSProgressFileAnimationImageKey$NSProgressFileAnimationImageOriginalRectKey$NSProgressFileCompletedCountKey$NSProgressFileIconKey$NSProgressFileOperationKindCopying$NSProgressFileOperationKindDecompressingAfterDownloading$NSProgressFileOperationKindDownloading$NSProgressFileOperationKindDuplicating$NSProgressFileOperationKindKey$NSProgressFileOperationKindReceiving$NSProgressFileOperationKindUploading$NSProgressFileTotalCountKey$NSProgressFileURLKey$NSProgressKindFile$NSProgressThroughputKey$NSRangeException$NSRecoveryAttempterErrorKey$NSRegistrationDomain$NSReplacementIndexAttributeName$NSRepublicOfChinaCalendar$NSRunLoopCommonModes$NSSecureUnarchiveFromDataTransformerName$NSShortDateFormatString$NSShortMonthNameArray$NSShortTimeDateFormatString$NSShortWeekDayNameArray$NSStreamDataWrittenToMemoryStreamKey$NSStreamFileCurrentOffsetKey$NSStreamNetworkServiceType$NSStreamNetworkServiceTypeBackground$NSStreamNetworkServiceTypeCallSignaling$NSStreamNetworkServiceTypeVideo$NSStreamNetworkServiceTypeVoIP$NSStreamNetworkServiceTypeVoice$NSStreamSOCKSErrorDomain$NSStreamSOCKSProxyConfigurationKey$NSStreamSOCKSProxyHostKey$NSStreamSOCKSProxyPasswordKey$NSStreamSOCKSProxyPortKey$NSStreamSOCKSProxyUserKey$NSStreamSOCKSProxyVersion4$NSStreamSOCKSProxyVersion5$NSStreamSOCKSProxyVersionKey$NSStreamSocketSSLErrorDomain$NSStreamSocketSecurityLevelKey$NSStreamSocketSecurityLevelNegotiatedSSL$NSStreamSocketSecurityLevelNone$NSStreamSocketSecurityLevelSSLv2$NSStreamSocketSecurityLevelSSLv3$NSStreamSocketSecurityLevelTLSv1$NSStringEncodingDetectionAllowLossyKey$NSStringEncodingDetectionDisallowedEncodingsKey$NSStringEncodingDetectionFromWindowsKey$NSStringEncodingDetectionLikelyLanguageKey$NSStringEncodingDetectionLossySubstitutionKey$NSStringEncodingDetectionSuggestedEncodingsKey$NSStringEncodingDetectionUseOnlySuggestedEncodingsKey$NSStringEncodingErrorKey$NSStringTransformFullwidthToHalfwidth$NSStringTransformHiraganaToKatakana$NSStringTransformLatinToArabic$NSStringTransformLatinToCyrillic$NSStringTransformLatinToGreek$NSStringTransformLatinToHangul$NSStringTransformLatinToHebrew$NSStringTransformLatinToHiragana$NSStringTransformLatinToKatakana$NSStringTransformLatinToThai$NSStringTransformMandarinToLatin$NSStringTransformStripCombiningMarks$NSStringTransformStripDiacritics$NSStringTransformToLatin$NSStringTransformToUnicodeName$NSStringTransformToXMLHex$NSSumKeyValueOperator$NSSystemClockDidChangeNotification$NSSystemTimeZoneDidChangeNotification$NSTaskDidTerminateNotification$NSTextCheckingAirlineKey$NSTextCheckingCityKey$NSTextCheckingCountryKey$NSTextCheckingFlightKey$NSTextCheckingJobTitleKey$NSTextCheckingNameKey$NSTextCheckingOrganizationKey$NSTextCheckingPhoneKey$NSTextCheckingStateKey$NSTextCheckingStreetKey$NSTextCheckingZIPKey$NSThisDayDesignations$NSThousandsSeparator$NSThreadWillExitNotification$NSThumbnail1024x1024SizeKey$NSTimeDateFormatString$NSTimeFormatString$NSURLAddedToDirectoryDateKey$NSURLApplicationIsScriptableKey$NSURLAttributeModificationDateKey$NSURLAuthenticationMethodClientCertificate$NSURLAuthenticationMethodDefault$NSURLAuthenticationMethodHTMLForm$NSURLAuthenticationMethodHTTPBasic$NSURLAuthenticationMethodHTTPDigest$NSURLAuthenticationMethodNTLM$NSURLAuthenticationMethodNegotiate$NSURLAuthenticationMethodServerTrust$NSURLCanonicalPathKey$NSURLContentAccessDateKey$NSURLContentModificationDateKey$NSURLContentTypeKey$NSURLCreationDateKey$NSURLCredentialStorageChangedNotification$NSURLCredentialStorageRemoveSynchronizableCredentials$NSURLCustomIconKey$NSURLDirectoryEntryCountKey$NSURLDocumentIdentifierKey$NSURLEffectiveIconKey$NSURLErrorBackgroundTaskCancelledReasonKey$NSURLErrorDomain$NSURLErrorFailingURLErrorKey$NSURLErrorFailingURLPeerTrustErrorKey$NSURLErrorFailingURLStringErrorKey$NSURLErrorKey$NSURLErrorNetworkUnavailableReasonKey$NSURLFileAllocatedSizeKey$NSURLFileContentIdentifierKey$NSURLFileIdentifierKey$NSURLFileProtectionComplete$NSURLFileProtectionCompleteUnlessOpen$NSURLFileProtectionCompleteUntilFirstUserAuthentication$NSURLFileProtectionCompleteWhenUserInactive$NSURLFileProtectionKey$NSURLFileProtectionNone$NSURLFileResourceIdentifierKey$NSURLFileResourceTypeBlockSpecial$NSURLFileResourceTypeCharacterSpecial$NSURLFileResourceTypeDirectory$NSURLFileResourceTypeKey$NSURLFileResourceTypeNamedPipe$NSURLFileResourceTypeRegular$NSURLFileResourceTypeSocket$NSURLFileResourceTypeSymbolicLink$NSURLFileResourceTypeUnknown$NSURLFileScheme$NSURLFileSecurityKey$NSURLFileSizeKey$NSURLGenerationIdentifierKey$NSURLHasHiddenExtensionKey$NSURLIsAliasFileKey$NSURLIsApplicationKey$NSURLIsDirectoryKey$NSURLIsExcludedFromBackupKey$NSURLIsExecutableKey$NSURLIsHiddenKey$NSURLIsMountTriggerKey$NSURLIsPackageKey$NSURLIsPurgeableKey$NSURLIsReadableKey$NSURLIsRegularFileKey$NSURLIsSparseKey$NSURLIsSymbolicLinkKey$NSURLIsSystemImmutableKey$NSURLIsUbiquitousItemKey$NSURLIsUserImmutableKey$NSURLIsVolumeKey$NSURLIsWritableKey$NSURLKeysOfUnsetValuesKey$NSURLLabelColorKey$NSURLLabelNumberKey$NSURLLinkCountKey$NSURLLocalizedLabelKey$NSURLLocalizedNameKey$NSURLLocalizedTypeDescriptionKey$NSURLMayHaveExtendedAttributesKey$NSURLMayShareFileContentKey$NSURLNameKey$NSURLParentDirectoryURLKey$NSURLPathKey$NSURLPreferredIOBlockSizeKey$NSURLProtectionSpaceFTP$NSURLProtectionSpaceFTPProxy$NSURLProtectionSpaceHTTP$NSURLProtectionSpaceHTTPProxy$NSURLProtectionSpaceHTTPS$NSURLProtectionSpaceHTTPSProxy$NSURLProtectionSpaceSOCKSProxy$NSURLQuarantinePropertiesKey$NSURLSessionDownloadTaskResumeData$NSURLSessionTaskPriorityDefault@f$NSURLSessionTaskPriorityHigh@f$NSURLSessionTaskPriorityLow@f$NSURLSessionTransferSizeUnknown@q$NSURLSessionUploadTaskResumeData$NSURLTagNamesKey$NSURLThumbnailDictionaryKey$NSURLThumbnailKey$NSURLTotalFileAllocatedSizeKey$NSURLTotalFileSizeKey$NSURLTypeIdentifierKey$NSURLUbiquitousItemContainerDisplayNameKey$NSURLUbiquitousItemDownloadRequestedKey$NSURLUbiquitousItemDownloadingErrorKey$NSURLUbiquitousItemDownloadingStatusCurrent$NSURLUbiquitousItemDownloadingStatusDownloaded$NSURLUbiquitousItemDownloadingStatusKey$NSURLUbiquitousItemDownloadingStatusNotDownloaded$NSURLUbiquitousItemHasUnresolvedConflictsKey$NSURLUbiquitousItemIsDownloadedKey$NSURLUbiquitousItemIsDownloadingKey$NSURLUbiquitousItemIsExcludedFromSyncKey$NSURLUbiquitousItemIsSharedKey$NSURLUbiquitousItemIsUploadedKey$NSURLUbiquitousItemIsUploadingKey$NSURLUbiquitousItemPercentDownloadedKey$NSURLUbiquitousItemPercentUploadedKey$NSURLUbiquitousItemUploadingErrorKey$NSURLUbiquitousSharedItemCurrentUserPermissionsKey$NSURLUbiquitousSharedItemCurrentUserRoleKey$NSURLUbiquitousSharedItemMostRecentEditorNameComponentsKey$NSURLUbiquitousSharedItemOwnerNameComponentsKey$NSURLUbiquitousSharedItemPermissionsReadOnly$NSURLUbiquitousSharedItemPermissionsReadWrite$NSURLUbiquitousSharedItemRoleOwner$NSURLUbiquitousSharedItemRoleParticipant$NSURLVolumeAvailableCapacityForImportantUsageKey$NSURLVolumeAvailableCapacityForOpportunisticUsageKey$NSURLVolumeAvailableCapacityKey$NSURLVolumeCreationDateKey$NSURLVolumeIdentifierKey$NSURLVolumeIsAutomountedKey$NSURLVolumeIsBrowsableKey$NSURLVolumeIsEjectableKey$NSURLVolumeIsEncryptedKey$NSURLVolumeIsInternalKey$NSURLVolumeIsJournalingKey$NSURLVolumeIsLocalKey$NSURLVolumeIsReadOnlyKey$NSURLVolumeIsRemovableKey$NSURLVolumeIsRootFileSystemKey$NSURLVolumeLocalizedFormatDescriptionKey$NSURLVolumeLocalizedNameKey$NSURLVolumeMaximumFileSizeKey$NSURLVolumeMountFromLocationKey$NSURLVolumeNameKey$NSURLVolumeResourceCountKey$NSURLVolumeSubtypeKey$NSURLVolumeSupportsAccessPermissionsKey$NSURLVolumeSupportsAdvisoryFileLockingKey$NSURLVolumeSupportsCasePreservedNamesKey$NSURLVolumeSupportsCaseSensitiveNamesKey$NSURLVolumeSupportsCompressionKey$NSURLVolumeSupportsExclusiveRenamingKey$NSURLVolumeSupportsExtendedSecurityKey$NSURLVolumeSupportsFileCloningKey$NSURLVolumeSupportsFileProtectionKey$NSURLVolumeSupportsHardLinksKey$NSURLVolumeSupportsImmutableFilesKey$NSURLVolumeSupportsJournalingKey$NSURLVolumeSupportsPersistentIDsKey$NSURLVolumeSupportsRenamingKey$NSURLVolumeSupportsRootDirectoryDatesKey$NSURLVolumeSupportsSparseFilesKey$NSURLVolumeSupportsSwapRenamingKey$NSURLVolumeSupportsSymbolicLinksKey$NSURLVolumeSupportsVolumeSizesKey$NSURLVolumeSupportsZeroRunsKey$NSURLVolumeTotalCapacityKey$NSURLVolumeTypeNameKey$NSURLVolumeURLForRemountingKey$NSURLVolumeURLKey$NSURLVolumeUUIDStringKey$NSUbiquitousKeyValueStoreChangeReasonKey$NSUbiquitousKeyValueStoreChangedKeysKey$NSUbiquitousKeyValueStoreDidChangeExternallyNotification$NSUbiquitousUserDefaultsCompletedInitialSyncNotification$NSUbiquitousUserDefaultsDidChangeAccountsNotification$NSUbiquitousUserDefaultsNoCloudAccountNotification$NSUbiquityIdentityDidChangeNotification$NSUnarchiveFromDataTransformerName$NSUndefinedKeyException$NSUnderlyingErrorKey$NSUndoManagerCheckpointNotification$NSUndoManagerDidCloseUndoGroupNotification$NSUndoManagerDidOpenUndoGroupNotification$NSUndoManagerDidRedoChangeNotification$NSUndoManagerDidUndoChangeNotification$NSUndoManagerGroupIsDiscardableKey$NSUndoManagerWillCloseUndoGroupNotification$NSUndoManagerWillRedoChangeNotification$NSUndoManagerWillUndoChangeNotification$NSUnionOfArraysKeyValueOperator$NSUnionOfObjectsKeyValueOperator$NSUnionOfSetsKeyValueOperator$NSUserActivityTypeBrowsingWeb$NSUserDefaultsDidChangeNotification$NSUserDefaultsSizeLimitExceededNotification$NSUserNotificationDefaultSoundName$NSWeekDayNameArray$NSWillBecomeMultiThreadedNotification$NSXMLParserErrorDomain$NSYearMonthWeekDesignations$NSZeroPoint@{CGPoint=dd}$NSZeroRect@{CGRect={CGPoint=dd}{CGSize=dd}}$NSZeroSize@{CGSize=dd}$NSZombieEnabled@Z$"""
enums = """$NSASCIIStringEncoding@1$NSActivityAnimationTrackingEnabled@35184372088832$NSActivityAutomaticTerminationDisabled@32768$NSActivityBackground@255$NSActivityIdleDisplaySleepDisabled@1099511627776$NSActivityIdleSystemSleepDisabled@1048576$NSActivityLatencyCritical@1095216660480$NSActivitySuddenTerminationDisabled@16384$NSActivityTrackingEnabled@70368744177664$NSActivityUserInitiated@16777215$NSActivityUserInitiatedAllowingIdleSystemSleep@15728639$NSActivityUserInteractive@1095233437695$NSAdminApplicationDirectory@4$NSAggregateExpressionType@14$NSAlignAllEdgesInward@15$NSAlignAllEdgesNearest@983040$NSAlignAllEdgesOutward@3840$NSAlignHeightInward@32$NSAlignHeightNearest@2097152$NSAlignHeightOutward@8192$NSAlignMaxXInward@4$NSAlignMaxXNearest@262144$NSAlignMaxXOutward@1024$NSAlignMaxYInward@8$NSAlignMaxYNearest@524288$NSAlignMaxYOutward@2048$NSAlignMinXInward@1$NSAlignMinXNearest@65536$NSAlignMinXOutward@256$NSAlignMinYInward@2$NSAlignMinYNearest@131072$NSAlignMinYOutward@512$NSAlignRectFlipped@9223372036854775808$NSAlignWidthInward@16$NSAlignWidthNearest@1048576$NSAlignWidthOutward@4096$NSAllApplicationsDirectory@100$NSAllDomainsMask@65535$NSAllLibrariesDirectory@101$NSAllPredicateModifier@1$NSAnchoredSearch@8$NSAndPredicateType@1$NSAnyKeyExpressionType@15$NSAnyPredicateModifier@2$NSAppleEventSendAlwaysInteract@48$NSAppleEventSendCanInteract@32$NSAppleEventSendCanSwitchLayer@64$NSAppleEventSendDefaultOptions@35$NSAppleEventSendDontAnnotate@65536$NSAppleEventSendDontExecute@8192$NSAppleEventSendDontRecord@4096$NSAppleEventSendNeverInteract@16$NSAppleEventSendNoReply@1$NSAppleEventSendQueueReply@2$NSAppleEventSendWaitForReply@3$NSApplicationDirectory@1$NSApplicationScriptsDirectory@23$NSApplicationSupportDirectory@14$NSArgumentEvaluationScriptError@3$NSArgumentsWrongScriptError@6$NSAtomicWrite@1$NSAttributedStringEnumerationLongestEffectiveRangeNotRequired@1048576$NSAttributedStringEnumerationReverse@2$NSAttributedStringFormattingApplyReplacementIndexAttribute@2$NSAttributedStringFormattingInsertArgumentAttributesWithoutMerging@1$NSAttributedStringMarkdownInterpretedSyntaxFull@0$NSAttributedStringMarkdownInterpretedSyntaxInlineOnly@1$NSAttributedStringMarkdownInterpretedSyntaxInlineOnlyPreservingWhitespace@2$NSAttributedStringMarkdownParsingFailureReturnError@0$NSAttributedStringMarkdownParsingFailureReturnPartiallyParsedIfPossible@1$NSAutosavedInformationDirectory@11$NSBackgroundActivityResultDeferred@2$NSBackgroundActivityResultFinished@1$NSBackwardsSearch@4$NSBeginsWithComparison@5$NSBeginsWithPredicateOperatorType@8$NSBetweenPredicateOperatorType@100$NSBinarySearchingFirstEqual@256$NSBinarySearchingInsertionIndex@1024$NSBinarySearchingLastEqual@512$NSBlockExpressionType@19$NSBundleErrorMaximum@5119$NSBundleErrorMinimum@4992$NSBundleExecutableArchitectureARM64@16777228$NSBundleExecutableArchitectureI386@7$NSBundleExecutableArchitecturePPC@18$NSBundleExecutableArchitecturePPC64@16777234$NSBundleExecutableArchitectureX86_64@16777223$NSBundleOnDemandResourceExceededMaximumSizeError@4993$NSBundleOnDemandResourceInvalidTagError@4994$NSBundleOnDemandResourceOutOfSpaceError@4992$NSByteCountFormatterCountStyleBinary@3$NSByteCountFormatterCountStyleDecimal@2$NSByteCountFormatterCountStyleFile@0$NSByteCountFormatterCountStyleMemory@1$NSByteCountFormatterUseAll@65535$NSByteCountFormatterUseBytes@1$NSByteCountFormatterUseDefault@0$NSByteCountFormatterUseEB@64$NSByteCountFormatterUseGB@8$NSByteCountFormatterUseKB@2$NSByteCountFormatterUseMB@4$NSByteCountFormatterUsePB@32$NSByteCountFormatterUseTB@16$NSByteCountFormatterUseYBOrHigher@65280$NSByteCountFormatterUseZB@128$NSCachesDirectory@13$NSCalculationDivideByZero@4$NSCalculationLossOfPrecision@1$NSCalculationNoError@0$NSCalculationOverflow@3$NSCalculationUnderflow@2$NSCalendarCalendarUnit@1048576$NSCalendarMatchFirst@4096$NSCalendarMatchLast@8192$NSCalendarMatchNextTime@1024$NSCalendarMatchNextTimePreservingSmallerUnits@512$NSCalendarMatchPreviousTimePreservingSmallerUnits@256$NSCalendarMatchStrictly@2$NSCalendarSearchBackwards@4$NSCalendarUnitCalendar@1048576$NSCalendarUnitDay@16$NSCalendarUnitDayOfYear@65536$NSCalendarUnitEra@2$NSCalendarUnitHour@32$NSCalendarUnitMinute@64$NSCalendarUnitMonth@8$NSCalendarUnitNanosecond@32768$NSCalendarUnitQuarter@2048$NSCalendarUnitSecond@128$NSCalendarUnitTimeZone@2097152$NSCalendarUnitWeekOfMonth@4096$NSCalendarUnitWeekOfYear@8192$NSCalendarUnitWeekday@512$NSCalendarUnitWeekdayOrdinal@1024$NSCalendarUnitYear@4$NSCalendarUnitYearForWeekOfYear@16384$NSCalendarWrapComponents@1$NSCannotCreateScriptCommandError@10$NSCaseInsensitivePredicateOption@1$NSCaseInsensitiveSearch@1$NSCloudSharingConflictError@5123$NSCloudSharingErrorMaximum@5375$NSCloudSharingErrorMinimum@5120$NSCloudSharingNetworkFailureError@5120$NSCloudSharingNoPermissionError@5124$NSCloudSharingOtherError@5375$NSCloudSharingQuotaExceededError@5121$NSCloudSharingTooManyParticipantsError@5122$NSCoderErrorMaximum@4991$NSCoderErrorMinimum@4864$NSCoderInvalidValueError@4866$NSCoderReadCorruptError@4864$NSCoderValueNotFoundError@4865$NSCollectionChangeInsert@0$NSCollectionChangeRemove@1$NSCollectorDisabledOption@2$NSCompressionErrorMaximum@5503$NSCompressionErrorMinimum@5376$NSCompressionFailedError@5376$NSConditionalExpressionType@20$NSConstantValueExpressionType@0$NSContainerSpecifierError@2$NSContainsComparison@7$NSContainsPredicateOperatorType@99$NSCoreServiceDirectory@10$NSCustomSelectorPredicateOperatorType@11$NSDataBase64DecodingIgnoreUnknownCharacters@1$NSDataBase64Encoding64CharacterLineLength@1$NSDataBase64Encoding76CharacterLineLength@2$NSDataBase64EncodingEndLineWithCarriageReturn@16$NSDataBase64EncodingEndLineWithLineFeed@32$NSDataCompressionAlgorithmLZ4@1$NSDataCompressionAlgorithmLZFSE@0$NSDataCompressionAlgorithmLZMA@2$NSDataCompressionAlgorithmZlib@3$NSDataReadingMapped@1$NSDataReadingMappedAlways@8$NSDataReadingMappedIfSafe@1$NSDataReadingUncached@2$NSDataSearchAnchored@2$NSDataSearchBackwards@1$NSDataWritingAtomic@1$NSDataWritingFileProtectionComplete@536870912$NSDataWritingFileProtectionCompleteUnlessOpen@805306368$NSDataWritingFileProtectionCompleteUntilFirstUserAuthentication@1073741824$NSDataWritingFileProtectionCompleteWhenUserInactive@1342177280$NSDataWritingFileProtectionMask@4026531840$NSDataWritingFileProtectionNone@268435456$NSDataWritingWithoutOverwriting@2$NSDateComponentUndefined@9223372036854775807$NSDateComponentsFormatterUnitsStyleAbbreviated@1$NSDateComponentsFormatterUnitsStyleBrief@5$NSDateComponentsFormatterUnitsStyleFull@3$NSDateComponentsFormatterUnitsStylePositional@0$NSDateComponentsFormatterUnitsStyleShort@2$NSDateComponentsFormatterUnitsStyleSpellOut@4$NSDateComponentsFormatterZeroFormattingBehaviorDefault@1$NSDateComponentsFormatterZeroFormattingBehaviorDropAll@14$NSDateComponentsFormatterZeroFormattingBehaviorDropLeading@2$NSDateComponentsFormatterZeroFormattingBehaviorDropMiddle@4$NSDateComponentsFormatterZeroFormattingBehaviorDropTrailing@8$NSDateComponentsFormatterZeroFormattingBehaviorNone@0$NSDateComponentsFormatterZeroFormattingBehaviorPad@65536$NSDateFormatterBehavior10_0@1000$NSDateFormatterBehavior10_4@1040$NSDateFormatterBehaviorDefault@0$NSDateFormatterFullStyle@4$NSDateFormatterLongStyle@3$NSDateFormatterMediumStyle@2$NSDateFormatterNoStyle@0$NSDateFormatterShortStyle@1$NSDateIntervalFormatterFullStyle@4$NSDateIntervalFormatterLongStyle@3$NSDateIntervalFormatterMediumStyle@2$NSDateIntervalFormatterNoStyle@0$NSDateIntervalFormatterShortStyle@1$NSDayCalendarUnit@16$NSDecimalMaxSize@8$NSDecodingFailurePolicyRaiseException@0$NSDecodingFailurePolicySetErrorAndReturn@1$NSDecompressionFailedError@5377$NSDemoApplicationDirectory@2$NSDesktopDirectory@12$NSDeveloperApplicationDirectory@3$NSDeveloperDirectory@6$NSDiacriticInsensitivePredicateOption@2$NSDiacriticInsensitiveSearch@128$NSDirectPredicateModifier@0$NSDirectoryEnumerationIncludesDirectoriesPostOrder@8$NSDirectoryEnumerationProducesRelativePathURLs@16$NSDirectoryEnumerationSkipsHiddenFiles@4$NSDirectoryEnumerationSkipsPackageDescendants@2$NSDirectoryEnumerationSkipsSubdirectoryDescendants@1$NSDistributedNotificationDeliverImmediately@1$NSDistributedNotificationPostToAllSessions@2$NSDocumentDirectory@9$NSDocumentationDirectory@8$NSDownloadsDirectory@15$NSEDGEINSETS_DEFINED@1$NSEndsWithComparison@6$NSEndsWithPredicateOperatorType@9$NSEnergyFormatterUnitCalorie@1793$NSEnergyFormatterUnitJoule@11$NSEnergyFormatterUnitKilocalorie@1794$NSEnergyFormatterUnitKilojoule@14$NSEnumerationConcurrent@1$NSEnumerationReverse@2$NSEqualToComparison@0$NSEqualToPredicateOperatorType@4$NSEraCalendarUnit@2$NSEvaluatedObjectExpressionType@1$NSEverySubelement@1$NSExecutableArchitectureMismatchError@3585$NSExecutableErrorMaximum@3839$NSExecutableErrorMinimum@3584$NSExecutableLinkError@3588$NSExecutableLoadError@3587$NSExecutableNotLoadableError@3584$NSExecutableRuntimeMismatchError@3586$NSFeatureUnsupportedError@3328$NSFileCoordinatorReadingForUploading@8$NSFileCoordinatorReadingImmediatelyAvailableMetadataOnly@4$NSFileCoordinatorReadingResolvesSymbolicLink@2$NSFileCoordinatorReadingWithoutChanges@1$NSFileCoordinatorWritingContentIndependentMetadataOnly@16$NSFileCoordinatorWritingForDeleting@1$NSFileCoordinatorWritingForMerging@4$NSFileCoordinatorWritingForMoving@2$NSFileCoordinatorWritingForReplacing@8$NSFileErrorMaximum@1023$NSFileErrorMinimum@0$NSFileLockingError@255$NSFileManagerItemReplacementUsingNewMetadataOnly@1$NSFileManagerItemReplacementWithoutDeletingBackupItem@2$NSFileManagerUnmountAllPartitionsAndEjectDisk@1$NSFileManagerUnmountBusyError@769$NSFileManagerUnmountUnknownError@768$NSFileManagerUnmountWithoutUI@2$NSFileNoSuchFileError@4$NSFileReadCorruptFileError@259$NSFileReadInapplicableStringEncodingError@261$NSFileReadInvalidFileNameError@258$NSFileReadNoPermissionError@257$NSFileReadNoSuchFileError@260$NSFileReadTooLargeError@263$NSFileReadUnknownError@256$NSFileReadUnknownStringEncodingError@264$NSFileReadUnsupportedSchemeError@262$NSFileVersionAddingByMoving@1$NSFileVersionReplacingByMoving@1$NSFileWrapperReadingImmediate@1$NSFileWrapperReadingWithoutMapping@2$NSFileWrapperWritingAtomic@1$NSFileWrapperWritingWithNameUpdating@2$NSFileWriteFileExistsError@516$NSFileWriteInapplicableStringEncodingError@517$NSFileWriteInvalidFileNameError@514$NSFileWriteNoPermissionError@513$NSFileWriteOutOfSpaceError@640$NSFileWriteUnknownError@512$NSFileWriteUnsupportedSchemeError@518$NSFileWriteVolumeReadOnlyError@642$NSForcedOrderingSearch@512$NSFormattingContextBeginningOfSentence@4$NSFormattingContextDynamic@1$NSFormattingContextListItem@3$NSFormattingContextMiddleOfSentence@5$NSFormattingContextStandalone@2$NSFormattingContextUnknown@0$NSFormattingError@2048$NSFormattingErrorMaximum@2559$NSFormattingErrorMinimum@2048$NSFormattingUnitStyleLong@3$NSFormattingUnitStyleMedium@2$NSFormattingUnitStyleShort@1$NSFoundationVersionNumber10_10@1151.16$NSFoundationVersionNumber10_10_1@1151.16$NSFoundationVersionNumber10_10_2@1152.14$NSFoundationVersionNumber10_10_3@1153.2$NSFoundationVersionNumber10_10_4@1153.2$NSFoundationVersionNumber10_10_5@1154.0$NSFoundationVersionNumber10_10_Max@1199.0$NSFoundationVersionNumber10_11@1252.0$NSFoundationVersionNumber10_11_1@1255.1$NSFoundationVersionNumber10_11_2@1256.1$NSFoundationVersionNumber10_11_3@1256.1$NSFoundationVersionNumber10_11_4@1258.0$NSFoundationVersionNumber10_11_Max@1299.0$NSFoundationVersionNumber10_8@945.0$NSFoundationVersionNumber10_8_1@945.0$NSFoundationVersionNumber10_8_2@945.11$NSFoundationVersionNumber10_8_3@945.16$NSFoundationVersionNumber10_8_4@945.18$NSFoundationVersionNumber10_9@1056$NSFoundationVersionNumber10_9_1@1056$NSFoundationVersionNumber10_9_2@1056.13$NSFoundationVersionWithFileManagerResourceForkSupport@412$NSFunctionExpressionType@4$NSGEOMETRY_TYPES_SAME_AS_CGGEOMETRY_TYPES@1$NSGrammaticalCaseAblative@6$NSGrammaticalCaseAccusative@2$NSGrammaticalCaseAdessive@7$NSGrammaticalCaseAllative@8$NSGrammaticalCaseDative@3$NSGrammaticalCaseElative@9$NSGrammaticalCaseEssive@11$NSGrammaticalCaseGenitive@4$NSGrammaticalCaseIllative@10$NSGrammaticalCaseInessive@12$NSGrammaticalCaseLocative@13$NSGrammaticalCaseNominative@1$NSGrammaticalCaseNotSet@0$NSGrammaticalCasePrepositional@5$NSGrammaticalCaseTranslative@14$NSGrammaticalDefinitenessDefinite@2$NSGrammaticalDefinitenessIndefinite@1$NSGrammaticalDefinitenessNotSet@0$NSGrammaticalDeterminationDependent@2$NSGrammaticalDeterminationIndependent@1$NSGrammaticalDeterminationNotSet@0$NSGrammaticalGenderFeminine@1$NSGrammaticalGenderMasculine@2$NSGrammaticalGenderNeuter@3$NSGrammaticalGenderNotSet@0$NSGrammaticalNumberNotSet@0$NSGrammaticalNumberPlural@3$NSGrammaticalNumberPluralFew@5$NSGrammaticalNumberPluralMany@6$NSGrammaticalNumberPluralTwo@4$NSGrammaticalNumberSingular@1$NSGrammaticalNumberZero@2$NSGrammaticalPartOfSpeechAbbreviation@14$NSGrammaticalPartOfSpeechAdjective@6$NSGrammaticalPartOfSpeechAdposition@7$NSGrammaticalPartOfSpeechAdverb@4$NSGrammaticalPartOfSpeechConjunction@10$NSGrammaticalPartOfSpeechDeterminer@1$NSGrammaticalPartOfSpeechInterjection@12$NSGrammaticalPartOfSpeechLetter@3$NSGrammaticalPartOfSpeechNotSet@0$NSGrammaticalPartOfSpeechNoun@9$NSGrammaticalPartOfSpeechNumeral@11$NSGrammaticalPartOfSpeechParticle@5$NSGrammaticalPartOfSpeechPreposition@13$NSGrammaticalPartOfSpeechPronoun@2$NSGrammaticalPartOfSpeechVerb@8$NSGrammaticalPersonFirst@1$NSGrammaticalPersonNotSet@0$NSGrammaticalPersonSecond@2$NSGrammaticalPersonThird@3$NSGrammaticalPronounTypeNotSet@0$NSGrammaticalPronounTypePersonal@1$NSGrammaticalPronounTypePossessive@3$NSGrammaticalPronounTypeReflexive@2$NSGreaterThanComparison@4$NSGreaterThanOrEqualToComparison@3$NSGreaterThanOrEqualToPredicateOperatorType@3$NSGreaterThanPredicateOperatorType@2$NSHPUXOperatingSystem@4$NSHTTPCookieAcceptPolicyAlways@0$NSHTTPCookieAcceptPolicyNever@1$NSHTTPCookieAcceptPolicyOnlyFromMainDocumentDomain@2$NSHashTableCopyIn@65536$NSHashTableObjectPointerPersonality@512$NSHashTableStrongMemory@0$NSHashTableWeakMemory@5$NSHashTableZeroingWeakMemory@1$NSHourCalendarUnit@32$NSINTEGER_DEFINED@1$NSISO2022JPStringEncoding@21$NSISO8601DateFormatWithColonSeparatorInTime@512$NSISO8601DateFormatWithColonSeparatorInTimeZone@1024$NSISO8601DateFormatWithDashSeparatorInDate@256$NSISO8601DateFormatWithDay@16$NSISO8601DateFormatWithFractionalSeconds@2048$NSISO8601DateFormatWithFullDate@275$NSISO8601DateFormatWithFullTime@1632$NSISO8601DateFormatWithInternetDateTime@1907$NSISO8601DateFormatWithMonth@2$NSISO8601DateFormatWithSpaceBetweenDateAndTime@128$NSISO8601DateFormatWithTime@32$NSISO8601DateFormatWithTimeZone@64$NSISO8601DateFormatWithWeekOfYear@4$NSISO8601DateFormatWithYear@1$NSISOLatin1StringEncoding@5$NSISOLatin2StringEncoding@9$NSInPredicateOperatorType@10$NSIndexSubelement@0$NSInlinePresentationIntentBlockHTML@512$NSInlinePresentationIntentCode@4$NSInlinePresentationIntentEmphasized@1$NSInlinePresentationIntentInlineHTML@256$NSInlinePresentationIntentLineBreak@128$NSInlinePresentationIntentSoftBreak@64$NSInlinePresentationIntentStrikethrough@32$NSInlinePresentationIntentStronglyEmphasized@2$NSInputMethodsDirectory@16$NSInternalScriptError@8$NSInternalSpecifierError@5$NSIntersectSetExpressionType@6$NSInvalidIndexSpecifierError@4$NSItemProviderFileOptionOpenInPlace@1$NSItemProviderItemUnavailableError@-1000$NSItemProviderRepresentationVisibilityAll@0$NSItemProviderRepresentationVisibilityGroup@2$NSItemProviderRepresentationVisibilityOwnProcess@3$NSItemProviderRepresentationVisibilityTeam@1$NSItemProviderUnavailableCoercionError@-1200$NSItemProviderUnexpectedValueClassError@-1100$NSItemProviderUnknownError@-1$NSItemReplacementDirectory@99$NSJSONReadingAllowFragments@4$NSJSONReadingFragmentsAllowed@4$NSJSONReadingJSON5Allowed@8$NSJSONReadingMutableContainers@1$NSJSONReadingMutableLeaves@2$NSJSONReadingTopLevelDictionaryAssumed@16$NSJSONWritingFragmentsAllowed@4$NSJSONWritingPrettyPrinted@1$NSJSONWritingSortedKeys@2$NSJSONWritingWithoutEscapingSlashes@8$NSJapaneseEUCStringEncoding@3$NSKeyPathExpressionType@3$NSKeySpecifierEvaluationScriptError@2$NSKeyValueChangeInsertion@2$NSKeyValueChangeRemoval@3$NSKeyValueChangeReplacement@4$NSKeyValueChangeSetting@1$NSKeyValueIntersectSetMutation@3$NSKeyValueMinusSetMutation@2$NSKeyValueObservingOptionInitial@4$NSKeyValueObservingOptionNew@1$NSKeyValueObservingOptionOld@2$NSKeyValueObservingOptionPrior@8$NSKeyValueSetSetMutation@4$NSKeyValueUnionSetMutation@1$NSKeyValueValidationError@1024$NSLengthFormatterUnitCentimeter@9$NSLengthFormatterUnitFoot@1282$NSLengthFormatterUnitInch@1281$NSLengthFormatterUnitKilometer@14$NSLengthFormatterUnitMeter@11$NSLengthFormatterUnitMile@1284$NSLengthFormatterUnitMillimeter@8$NSLengthFormatterUnitYard@1283$NSLessThanComparison@2$NSLessThanOrEqualToComparison@1$NSLessThanOrEqualToPredicateOperatorType@1$NSLessThanPredicateOperatorType@0$NSLibraryDirectory@5$NSLikePredicateOperatorType@7$NSLinguisticTaggerJoinNames@16$NSLinguisticTaggerOmitOther@8$NSLinguisticTaggerOmitPunctuation@2$NSLinguisticTaggerOmitWhitespace@4$NSLinguisticTaggerOmitWords@1$NSLinguisticTaggerUnitDocument@3$NSLinguisticTaggerUnitParagraph@2$NSLinguisticTaggerUnitSentence@1$NSLinguisticTaggerUnitWord@0$NSLiteralSearch@2$NSLocalDomainMask@2$NSLocaleLanguageDirectionBottomToTop@4$NSLocaleLanguageDirectionLeftToRight@1$NSLocaleLanguageDirectionRightToLeft@2$NSLocaleLanguageDirectionTopToBottom@3$NSLocaleLanguageDirectionUnknown@0$NSMACHOperatingSystem@5$NSMacOSRomanStringEncoding@30$NSMachPortDeallocateNone@0$NSMachPortDeallocateReceiveRight@2$NSMachPortDeallocateSendRight@1$NSMapTableCopyIn@65536$NSMapTableObjectPointerPersonality@512$NSMapTableStrongMemory@0$NSMapTableWeakMemory@5$NSMapTableZeroingWeakMemory@1$NSMappedRead@1$NSMassFormatterUnitGram@11$NSMassFormatterUnitKilogram@14$NSMassFormatterUnitOunce@1537$NSMassFormatterUnitPound@1538$NSMassFormatterUnitStone@1539$NSMatchesPredicateOperatorType@6$NSMatchingAnchored@4$NSMatchingCompleted@2$NSMatchingHitEnd@4$NSMatchingInternalError@16$NSMatchingProgress@1$NSMatchingReportCompletion@2$NSMatchingReportProgress@1$NSMatchingRequiredEnd@8$NSMatchingWithTransparentBounds@8$NSMatchingWithoutAnchoringBounds@16$NSMaxXEdge@2$NSMaxYEdge@3$NSMaximumStringLength@9223372036854775807$NSMeasurementFormatterUnitOptionsNaturalScale@2$NSMeasurementFormatterUnitOptionsProvidedUnit@1$NSMeasurementFormatterUnitOptionsTemperatureWithoutUnit@4$NSMiddleSubelement@2$NSMinXEdge@0$NSMinYEdge@1$NSMinusSetExpressionType@7$NSMinuteCalendarUnit@64$NSMonthCalendarUnit@8$NSMoviesDirectory@17$NSMusicDirectory@18$NSNEXTSTEPStringEncoding@2$NSNetServiceListenForConnections@2$NSNetServiceNoAutoRename@1$NSNetServicesActivityInProgress@-72003$NSNetServicesBadArgumentError@-72004$NSNetServicesCancelledError@-72005$NSNetServicesCollisionError@-72001$NSNetServicesInvalidError@-72006$NSNetServicesMissingRequiredConfigurationError@-72008$NSNetServicesNotFoundError@-72002$NSNetServicesTimeoutError@-72007$NSNetServicesUnknownError@-72000$NSNetworkDomainMask@4$NSNoScriptError@0$NSNoSpecifierError@0$NSNoSubelement@4$NSNoTopLevelContainersSpecifierError@1$NSNonLossyASCIIStringEncoding@7$NSNormalizedPredicateOption@4$NSNotEqualToPredicateOperatorType@5$NSNotFound@9223372036854775807$NSNotPredicateType@0$NSNotificationCoalescingOnName@1$NSNotificationCoalescingOnSender@2$NSNotificationDeliverImmediately@1$NSNotificationNoCoalescing@0$NSNotificationPostToAllSessions@2$NSNotificationSuspensionBehaviorCoalesce@2$NSNotificationSuspensionBehaviorDeliverImmediately@4$NSNotificationSuspensionBehaviorDrop@1$NSNotificationSuspensionBehaviorHold@3$NSNumberFormatterBehavior10_0@1000$NSNumberFormatterBehavior10_4@1040$NSNumberFormatterBehaviorDefault@0$NSNumberFormatterCurrencyAccountingStyle@10$NSNumberFormatterCurrencyISOCodeStyle@8$NSNumberFormatterCurrencyPluralStyle@9$NSNumberFormatterCurrencyStyle@2$NSNumberFormatterDecimalStyle@1$NSNumberFormatterNoStyle@0$NSNumberFormatterOrdinalStyle@6$NSNumberFormatterPadAfterPrefix@1$NSNumberFormatterPadAfterSuffix@3$NSNumberFormatterPadBeforePrefix@0$NSNumberFormatterPadBeforeSuffix@2$NSNumberFormatterPercentStyle@3$NSNumberFormatterRoundCeiling@0$NSNumberFormatterRoundDown@2$NSNumberFormatterRoundFloor@1$NSNumberFormatterRoundHalfDown@5$NSNumberFormatterRoundHalfEven@4$NSNumberFormatterRoundHalfUp@6$NSNumberFormatterRoundUp@3$NSNumberFormatterScientificStyle@4$NSNumberFormatterSpellOutStyle@5$NSNumericSearch@64$NSOSF1OperatingSystem@7$NSObjectAutoreleasedEvent@3$NSObjectExtraRefDecrementedEvent@5$NSObjectExtraRefIncrementedEvent@4$NSObjectInternalRefDecrementedEvent@7$NSObjectInternalRefIncrementedEvent@6$NSOpenStepUnicodeReservedBase@62464$NSOperationNotSupportedForKeyScriptError@9$NSOperationNotSupportedForKeySpecifierError@6$NSOperationQueueDefaultMaxConcurrentOperationCount@-1$NSOperationQueuePriorityHigh@4$NSOperationQueuePriorityLow@-4$NSOperationQueuePriorityNormal@0$NSOperationQueuePriorityVeryHigh@8$NSOperationQueuePriorityVeryLow@-8$NSOrPredicateType@2$NSOrderedAscending@-1$NSOrderedCollectionDifferenceCalculationInferMoves@4$NSOrderedCollectionDifferenceCalculationOmitInsertedObjects@1$NSOrderedCollectionDifferenceCalculationOmitRemovedObjects@2$NSOrderedDescending@1$NSOrderedSame@0$NSPersonNameComponentsFormatterPhonetic@2$NSPersonNameComponentsFormatterStyleAbbreviated@4$NSPersonNameComponentsFormatterStyleDefault@0$NSPersonNameComponentsFormatterStyleLong@3$NSPersonNameComponentsFormatterStyleMedium@2$NSPersonNameComponentsFormatterStyleShort@1$NSPicturesDirectory@19$NSPointerFunctionsCStringPersonality@768$NSPointerFunctionsCopyIn@65536$NSPointerFunctionsIntegerPersonality@1280$NSPointerFunctionsMachVirtualMemory@4$NSPointerFunctionsMallocMemory@3$NSPointerFunctionsObjectPersonality@0$NSPointerFunctionsObjectPointerPersonality@512$NSPointerFunctionsOpaqueMemory@2$NSPointerFunctionsOpaquePersonality@256$NSPointerFunctionsStrongMemory@0$NSPointerFunctionsStructPersonality@1024$NSPointerFunctionsWeakMemory@5$NSPointerFunctionsZeroingWeakMemory@1$NSPositionAfter@0$NSPositionBefore@1$NSPositionBeginning@2$NSPositionEnd@3$NSPositionReplace@4$NSPostASAP@2$NSPostNow@3$NSPostWhenIdle@1$NSPreferencePanesDirectory@22$NSPresentationIntentKindBlockQuote@6$NSPresentationIntentKindCodeBlock@5$NSPresentationIntentKindHeader@1$NSPresentationIntentKindListItem@4$NSPresentationIntentKindOrderedList@2$NSPresentationIntentKindParagraph@0$NSPresentationIntentKindTable@8$NSPresentationIntentKindTableCell@11$NSPresentationIntentKindTableHeaderRow@9$NSPresentationIntentKindTableRow@10$NSPresentationIntentKindThematicBreak@7$NSPresentationIntentKindUnorderedList@3$NSPresentationIntentTableColumnAlignmentCenter@1$NSPresentationIntentTableColumnAlignmentLeft@0$NSPresentationIntentTableColumnAlignmentRight@2$NSPrinterDescriptionDirectory@20$NSProcessInfoThermalStateCritical@3$NSProcessInfoThermalStateFair@1$NSProcessInfoThermalStateNominal@0$NSProcessInfoThermalStateSerious@2$NSPropertyListBinaryFormat_v1_0@200$NSPropertyListErrorMaximum@4095$NSPropertyListErrorMinimum@3840$NSPropertyListImmutable@0$NSPropertyListMutableContainers@1$NSPropertyListMutableContainersAndLeaves@2$NSPropertyListOpenStepFormat@1$NSPropertyListReadCorruptError@3840$NSPropertyListReadStreamError@3842$NSPropertyListReadUnknownVersionError@3841$NSPropertyListWriteInvalidError@3852$NSPropertyListWriteStreamError@3851$NSPropertyListXMLFormat_v1_0@100$NSProprietaryStringEncoding@65536$NSQualityOfServiceBackground@9$NSQualityOfServiceDefault@-1$NSQualityOfServiceUserInitiated@25$NSQualityOfServiceUserInteractive@33$NSQualityOfServiceUtility@17$NSQuarterCalendarUnit@2048$NSRandomSubelement@3$NSReceiverEvaluationScriptError@1$NSReceiversCantHandleCommandScriptError@4$NSRectEdgeMaxX@2$NSRectEdgeMaxY@3$NSRectEdgeMinX@0$NSRectEdgeMinY@1$NSRegularExpressionAllowCommentsAndWhitespace@2$NSRegularExpressionAnchorsMatchLines@16$NSRegularExpressionCaseInsensitive@1$NSRegularExpressionDotMatchesLineSeparators@8$NSRegularExpressionIgnoreMetacharacters@4$NSRegularExpressionSearch@1024$NSRegularExpressionUseUnicodeWordBoundaries@64$NSRegularExpressionUseUnixLineSeparators@32$NSRelativeAfter@0$NSRelativeBefore@1$NSRelativeDateTimeFormatterStyleNamed@1$NSRelativeDateTimeFormatterStyleNumeric@0$NSRelativeDateTimeFormatterUnitsStyleAbbreviated@3$NSRelativeDateTimeFormatterUnitsStyleFull@0$NSRelativeDateTimeFormatterUnitsStyleShort@2$NSRelativeDateTimeFormatterUnitsStyleSpellOut@1$NSRequiredArgumentsMissingScriptError@5$NSRoundBankers@3$NSRoundDown@1$NSRoundPlain@0$NSRoundUp@2$NSSaveOptionsAsk@2$NSSaveOptionsNo@1$NSSaveOptionsYes@0$NSScannedOption@1$NSSecondCalendarUnit@128$NSSharedPublicDirectory@21$NSShiftJISStringEncoding@8$NSSolarisOperatingSystem@3$NSSortConcurrent@1$NSSortStable@16$NSStreamEventEndEncountered@16$NSStreamEventErrorOccurred@8$NSStreamEventHasBytesAvailable@2$NSStreamEventHasSpaceAvailable@4$NSStreamEventNone@0$NSStreamEventOpenCompleted@1$NSStreamStatusAtEnd@5$NSStreamStatusClosed@6$NSStreamStatusError@7$NSStreamStatusNotOpen@0$NSStreamStatusOpen@2$NSStreamStatusOpening@1$NSStreamStatusReading@3$NSStreamStatusWriting@4$NSStringEncodingConversionAllowLossy@1$NSStringEncodingConversionExternalRepresentation@2$NSStringEnumerationByCaretPositions@5$NSStringEnumerationByComposedCharacterSequences@2$NSStringEnumerationByDeletionClusters@6$NSStringEnumerationByLines@0$NSStringEnumerationByParagraphs@1$NSStringEnumerationBySentences@4$NSStringEnumerationByWords@3$NSStringEnumerationLocalized@1024$NSStringEnumerationReverse@256$NSStringEnumerationSubstringNotRequired@512$NSSubqueryExpressionType@13$NSSunOSOperatingSystem@6$NSSymbolStringEncoding@6$NSSystemDomainMask@8$NSTaskTerminationReasonExit@1$NSTaskTerminationReasonUncaughtSignal@2$NSTextCheckingAllCustomTypes@18446744069414584320$NSTextCheckingAllSystemTypes@4294967295$NSTextCheckingAllTypes@18446744073709551615$NSTextCheckingTypeAddress@16$NSTextCheckingTypeCorrection@512$NSTextCheckingTypeDash@128$NSTextCheckingTypeDate@8$NSTextCheckingTypeGrammar@4$NSTextCheckingTypeLink@32$NSTextCheckingTypeOrthography@1$NSTextCheckingTypePhoneNumber@2048$NSTextCheckingTypeQuote@64$NSTextCheckingTypeRegularExpression@1024$NSTextCheckingTypeReplacement@256$NSTextCheckingTypeSpelling@2$NSTextCheckingTypeTransitInformation@4096$NSTimeZoneCalendarUnit@2097152$NSTimeZoneNameStyleDaylightSaving@2$NSTimeZoneNameStyleGeneric@4$NSTimeZoneNameStyleShortDaylightSaving@3$NSTimeZoneNameStyleShortGeneric@5$NSTimeZoneNameStyleShortStandard@1$NSTimeZoneNameStyleStandard@0$NSTrashDirectory@102$NSURLBookmarkCreationMinimalBookmark@512$NSURLBookmarkCreationPreferFileIDResolution@256$NSURLBookmarkCreationSecurityScopeAllowOnlyReadAccess@4096$NSURLBookmarkCreationSuitableForBookmarkFile@1024$NSURLBookmarkCreationWithSecurityScope@2048$NSURLBookmarkCreationWithoutImplicitSecurityScope@536870912$NSURLBookmarkResolutionWithSecurityScope@1024$NSURLBookmarkResolutionWithoutImplicitStartAccessing@32768$NSURLBookmarkResolutionWithoutMounting@512$NSURLBookmarkResolutionWithoutUI@256$NSURLCacheStorageAllowed@0$NSURLCacheStorageAllowedInMemoryOnly@1$NSURLCacheStorageNotAllowed@2$NSURLCredentialPersistenceForSession@1$NSURLCredentialPersistenceNone@0$NSURLCredentialPersistencePermanent@2$NSURLCredentialPersistenceSynchronizable@3$NSURLErrorAppTransportSecurityRequiresSecureConnection@-1022$NSURLErrorBackgroundSessionInUseByAnotherProcess@-996$NSURLErrorBackgroundSessionRequiresSharedContainer@-995$NSURLErrorBackgroundSessionWasDisconnected@-997$NSURLErrorBadServerResponse@-1011$NSURLErrorBadURL@-1000$NSURLErrorCallIsActive@-1019$NSURLErrorCancelled@-999$NSURLErrorCancelledReasonBackgroundUpdatesDisabled@1$NSURLErrorCancelledReasonInsufficientSystemResources@2$NSURLErrorCancelledReasonUserForceQuitApplication@0$NSURLErrorCannotCloseFile@-3002$NSURLErrorCannotConnectToHost@-1004$NSURLErrorCannotCreateFile@-3000$NSURLErrorCannotDecodeContentData@-1016$NSURLErrorCannotDecodeRawData@-1015$NSURLErrorCannotFindHost@-1003$NSURLErrorCannotLoadFromNetwork@-2000$NSURLErrorCannotMoveFile@-3005$NSURLErrorCannotOpenFile@-3001$NSURLErrorCannotParseResponse@-1017$NSURLErrorCannotRemoveFile@-3004$NSURLErrorCannotWriteToFile@-3003$NSURLErrorClientCertificateRejected@-1205$NSURLErrorClientCertificateRequired@-1206$NSURLErrorDNSLookupFailed@-1006$NSURLErrorDataLengthExceedsMaximum@-1103$NSURLErrorDataNotAllowed@-1020$NSURLErrorDownloadDecodingFailedMidStream@-3006$NSURLErrorDownloadDecodingFailedToComplete@-3007$NSURLErrorFileDoesNotExist@-1100$NSURLErrorFileIsDirectory@-1101$NSURLErrorFileOutsideSafeArea@-1104$NSURLErrorHTTPTooManyRedirects@-1007$NSURLErrorInternationalRoamingOff@-1018$NSURLErrorNetworkConnectionLost@-1005$NSURLErrorNetworkUnavailableReasonCellular@0$NSURLErrorNetworkUnavailableReasonConstrained@2$NSURLErrorNetworkUnavailableReasonExpensive@1$NSURLErrorNoPermissionsToReadFile@-1102$NSURLErrorNotConnectedToInternet@-1009$NSURLErrorRedirectToNonExistentLocation@-1010$NSURLErrorRequestBodyStreamExhausted@-1021$NSURLErrorResourceUnavailable@-1008$NSURLErrorSecureConnectionFailed@-1200$NSURLErrorServerCertificateHasBadDate@-1201$NSURLErrorServerCertificateHasUnknownRoot@-1203$NSURLErrorServerCertificateNotYetValid@-1204$NSURLErrorServerCertificateUntrusted@-1202$NSURLErrorTimedOut@-1001$NSURLErrorUnknown@-1$NSURLErrorUnsupportedURL@-1002$NSURLErrorUserAuthenticationRequired@-1013$NSURLErrorUserCancelledAuthentication@-1012$NSURLErrorZeroByteResource@-1014$NSURLHandleLoadFailed@3$NSURLHandleLoadInProgress@2$NSURLHandleLoadSucceeded@1$NSURLHandleNotLoaded@0$NSURLNetworkServiceTypeAVStreaming@8$NSURLNetworkServiceTypeBackground@3$NSURLNetworkServiceTypeCallSignaling@11$NSURLNetworkServiceTypeDefault@0$NSURLNetworkServiceTypeResponsiveAV@9$NSURLNetworkServiceTypeResponsiveData@6$NSURLNetworkServiceTypeVideo@2$NSURLNetworkServiceTypeVoIP@1$NSURLNetworkServiceTypeVoice@4$NSURLRelationshipContains@0$NSURLRelationshipOther@2$NSURLRelationshipSame@1$NSURLRequestAttributionDeveloper@0$NSURLRequestAttributionUser@1$NSURLRequestReloadIgnoringCacheData@1$NSURLRequestReloadIgnoringLocalAndRemoteCacheData@4$NSURLRequestReloadIgnoringLocalCacheData@1$NSURLRequestReloadRevalidatingCacheData@5$NSURLRequestReturnCacheDataDontLoad@3$NSURLRequestReturnCacheDataElseLoad@2$NSURLRequestUseProtocolCachePolicy@0$NSURLResponseUnknownLength@-1$NSURLSessionAuthChallengeCancelAuthenticationChallenge@2$NSURLSessionAuthChallengePerformDefaultHandling@1$NSURLSessionAuthChallengeRejectProtectionSpace@3$NSURLSessionAuthChallengeUseCredential@0$NSURLSessionDelayedRequestCancel@2$NSURLSessionDelayedRequestContinueLoading@0$NSURLSessionDelayedRequestUseNewRequest@1$NSURLSessionMultipathServiceTypeAggregate@3$NSURLSessionMultipathServiceTypeHandover@1$NSURLSessionMultipathServiceTypeInteractive@2$NSURLSessionMultipathServiceTypeNone@0$NSURLSessionResponseAllow@1$NSURLSessionResponseBecomeDownload@2$NSURLSessionResponseBecomeStream@3$NSURLSessionResponseCancel@0$NSURLSessionTaskMetricsDomainResolutionProtocolHTTPS@4$NSURLSessionTaskMetricsDomainResolutionProtocolTCP@2$NSURLSessionTaskMetricsDomainResolutionProtocolTLS@3$NSURLSessionTaskMetricsDomainResolutionProtocolUDP@1$NSURLSessionTaskMetricsDomainResolutionProtocolUnknown@0$NSURLSessionTaskMetricsResourceFetchTypeLocalCache@3$NSURLSessionTaskMetricsResourceFetchTypeNetworkLoad@1$NSURLSessionTaskMetricsResourceFetchTypeServerPush@2$NSURLSessionTaskMetricsResourceFetchTypeUnknown@0$NSURLSessionTaskStateCanceling@2$NSURLSessionTaskStateCompleted@3$NSURLSessionTaskStateRunning@0$NSURLSessionTaskStateSuspended@1$NSURLSessionWebSocketCloseCodeAbnormalClosure@1006$NSURLSessionWebSocketCloseCodeGoingAway@1001$NSURLSessionWebSocketCloseCodeInternalServerError@1011$NSURLSessionWebSocketCloseCodeInvalid@0$NSURLSessionWebSocketCloseCodeInvalidFramePayloadData@1007$NSURLSessionWebSocketCloseCodeMandatoryExtensionMissing@1010$NSURLSessionWebSocketCloseCodeMessageTooBig@1009$NSURLSessionWebSocketCloseCodeNoStatusReceived@1005$NSURLSessionWebSocketCloseCodeNormalClosure@1000$NSURLSessionWebSocketCloseCodePolicyViolation@1008$NSURLSessionWebSocketCloseCodeProtocolError@1002$NSURLSessionWebSocketCloseCodeTLSHandshakeFailure@1015$NSURLSessionWebSocketCloseCodeUnsupportedData@1003$NSURLSessionWebSocketMessageTypeData@0$NSURLSessionWebSocketMessageTypeString@1$NSUTF16BigEndianStringEncoding@**********$NSUTF16LittleEndianStringEncoding@**********$NSUTF16StringEncoding@10$NSUTF32BigEndianStringEncoding@**********$NSUTF32LittleEndianStringEncoding@**********$NSUTF32StringEncoding@**********$NSUTF8StringEncoding@4$NSUbiquitousFileErrorMaximum@4607$NSUbiquitousFileErrorMinimum@4352$NSUbiquitousFileNotUploadedDueToQuotaError@4354$NSUbiquitousFileUbiquityServerNotAvailable@4355$NSUbiquitousFileUnavailableError@4353$NSUbiquitousKeyValueStoreAccountChange@3$NSUbiquitousKeyValueStoreInitialSyncChange@1$NSUbiquitousKeyValueStoreQuotaViolationChange@2$NSUbiquitousKeyValueStoreServerChange@0$NSUncachedRead@2$NSUndefinedDateComponent@9223372036854775807$NSUndoCloseGroupingRunLoopOrdering@350000$NSUnicodeStringEncoding@10$NSUnionSetExpressionType@5$NSUnknownKeyScriptError@7$NSUnknownKeySpecifierError@3$NSUserActivityConnectionUnavailableError@4609$NSUserActivityErrorMaximum@4863$NSUserActivityErrorMinimum@4608$NSUserActivityHandoffFailedError@4608$NSUserActivityHandoffUserInfoTooLargeError@4611$NSUserActivityRemoteApplicationTimedOutError@4610$NSUserCancelledError@3072$NSUserDirectory@7$NSUserDomainMask@1$NSUserNotificationActivationTypeActionButtonClicked@2$NSUserNotificationActivationTypeAdditionalActionClicked@4$NSUserNotificationActivationTypeContentsClicked@1$NSUserNotificationActivationTypeNone@0$NSUserNotificationActivationTypeReplied@3$NSValidationErrorMaximum@2047$NSValidationErrorMinimum@1024$NSVariableExpressionType@2$NSVolumeEnumerationProduceFileReferenceURLs@4$NSVolumeEnumerationSkipHiddenVolumes@2$NSWeekCalendarUnit@256$NSWeekOfMonthCalendarUnit@4096$NSWeekOfYearCalendarUnit@8192$NSWeekdayCalendarUnit@512$NSWeekdayOrdinalCalendarUnit@1024$NSWidthInsensitiveSearch@256$NSWindows95OperatingSystem@2$NSWindowsCP1250StringEncoding@15$NSWindowsCP1251StringEncoding@11$NSWindowsCP1252StringEncoding@12$NSWindowsCP1253StringEncoding@13$NSWindowsCP1254StringEncoding@14$NSWindowsNTOperatingSystem@1$NSWrapCalendarComponents@1$NSXMLAttributeCDATAKind@6$NSXMLAttributeDeclarationKind@10$NSXMLAttributeEntitiesKind@11$NSXMLAttributeEntityKind@10$NSXMLAttributeEnumerationKind@14$NSXMLAttributeIDKind@7$NSXMLAttributeIDRefKind@8$NSXMLAttributeIDRefsKind@9$NSXMLAttributeKind@3$NSXMLAttributeNMTokenKind@12$NSXMLAttributeNMTokensKind@13$NSXMLAttributeNotationKind@15$NSXMLCommentKind@6$NSXMLDTDKind@8$NSXMLDocumentHTMLKind@2$NSXMLDocumentIncludeContentTypeDeclaration@262144$NSXMLDocumentKind@1$NSXMLDocumentTextKind@3$NSXMLDocumentTidyHTML@512$NSXMLDocumentTidyXML@1024$NSXMLDocumentValidate@8192$NSXMLDocumentXHTMLKind@1$NSXMLDocumentXInclude@65536$NSXMLDocumentXMLKind@0$NSXMLElementDeclarationAnyKind@18$NSXMLElementDeclarationElementKind@20$NSXMLElementDeclarationEmptyKind@17$NSXMLElementDeclarationKind@11$NSXMLElementDeclarationMixedKind@19$NSXMLElementDeclarationUndefinedKind@16$NSXMLElementKind@2$NSXMLEntityDeclarationKind@9$NSXMLEntityGeneralKind@1$NSXMLEntityParameterKind@4$NSXMLEntityParsedKind@2$NSXMLEntityPredefined@5$NSXMLEntityUnparsedKind@3$NSXMLInvalidKind@0$NSXMLNamespaceKind@4$NSXMLNodeCompactEmptyElement@4$NSXMLNodeExpandEmptyElement@2$NSXMLNodeIsCDATA@1$NSXMLNodeLoadExternalEntitiesAlways@16384$NSXMLNodeLoadExternalEntitiesNever@524288$NSXMLNodeLoadExternalEntitiesSameOriginOnly@32768$NSXMLNodeNeverEscapeContents@32$NSXMLNodeOptionsNone@0$NSXMLNodePreserveAll@4293918750$NSXMLNodePreserveAttributeOrder@2097152$NSXMLNodePreserveCDATA@16777216$NSXMLNodePreserveCharacterReferences@134217728$NSXMLNodePreserveDTD@67108864$NSXMLNodePreserveEmptyElements@6$NSXMLNodePreserveEntities@4194304$NSXMLNodePreserveNamespaceOrder@1048576$NSXMLNodePreservePrefixes@8388608$NSXMLNodePreserveQuotes@24$NSXMLNodePreserveWhitespace@33554432$NSXMLNodePrettyPrint@131072$NSXMLNodePromoteSignificantWhitespace@268435456$NSXMLNodeUseDoubleQuotes@16$NSXMLNodeUseSingleQuotes@8$NSXMLNotationDeclarationKind@12$NSXMLParserAttributeHasNoValueError@41$NSXMLParserAttributeListNotFinishedError@51$NSXMLParserAttributeListNotStartedError@50$NSXMLParserAttributeNotFinishedError@40$NSXMLParserAttributeNotStartedError@39$NSXMLParserAttributeRedefinedError@42$NSXMLParserCDATANotFinishedError@63$NSXMLParserCharacterRefAtEOFError@10$NSXMLParserCharacterRefInDTDError@13$NSXMLParserCharacterRefInEpilogError@12$NSXMLParserCharacterRefInPrologError@11$NSXMLParserCommentContainsDoubleHyphenError@80$NSXMLParserCommentNotFinishedError@45$NSXMLParserConditionalSectionNotFinishedError@59$NSXMLParserConditionalSectionNotStartedError@58$NSXMLParserDOCTYPEDeclNotFinishedError@61$NSXMLParserDelegateAbortedParseError@512$NSXMLParserDocumentStartError@3$NSXMLParserElementContentDeclNotFinishedError@55$NSXMLParserElementContentDeclNotStartedError@54$NSXMLParserEmptyDocumentError@4$NSXMLParserEncodingNotSupportedError@32$NSXMLParserEntityBoundaryError@90$NSXMLParserEntityIsExternalError@29$NSXMLParserEntityIsParameterError@30$NSXMLParserEntityNotFinishedError@37$NSXMLParserEntityNotStartedError@36$NSXMLParserEntityRefAtEOFError@14$NSXMLParserEntityRefInDTDError@17$NSXMLParserEntityRefInEpilogError@16$NSXMLParserEntityRefInPrologError@15$NSXMLParserEntityRefLoopError@89$NSXMLParserEntityReferenceMissingSemiError@23$NSXMLParserEntityReferenceWithoutNameError@22$NSXMLParserEntityValueRequiredError@84$NSXMLParserEqualExpectedError@75$NSXMLParserExternalStandaloneEntityError@82$NSXMLParserExternalSubsetNotFinishedError@60$NSXMLParserExtraContentError@86$NSXMLParserGTRequiredError@73$NSXMLParserInternalError@1$NSXMLParserInvalidCharacterError@9$NSXMLParserInvalidCharacterInEntityError@87$NSXMLParserInvalidCharacterRefError@8$NSXMLParserInvalidConditionalSectionError@83$NSXMLParserInvalidDecimalCharacterRefError@7$NSXMLParserInvalidEncodingError@81$NSXMLParserInvalidEncodingNameError@79$NSXMLParserInvalidHexCharacterRefError@6$NSXMLParserInvalidURIError@91$NSXMLParserLTRequiredError@72$NSXMLParserLTSlashRequiredError@74$NSXMLParserLessThanSymbolInAttributeError@38$NSXMLParserLiteralNotFinishedError@44$NSXMLParserLiteralNotStartedError@43$NSXMLParserMisplacedCDATAEndStringError@62$NSXMLParserMisplacedXMLDeclarationError@64$NSXMLParserMixedContentDeclNotFinishedError@53$NSXMLParserMixedContentDeclNotStartedError@52$NSXMLParserNAMERequiredError@68$NSXMLParserNMTOKENRequiredError@67$NSXMLParserNamespaceDeclarationError@35$NSXMLParserNoDTDError@94$NSXMLParserNotWellBalancedError@85$NSXMLParserNotationNotFinishedError@49$NSXMLParserNotationNotStartedError@48$NSXMLParserOutOfMemoryError@2$NSXMLParserPCDATARequiredError@69$NSXMLParserParsedEntityRefAtEOFError@18$NSXMLParserParsedEntityRefInEpilogError@20$NSXMLParserParsedEntityRefInInternalError@88$NSXMLParserParsedEntityRefInInternalSubsetError@21$NSXMLParserParsedEntityRefInPrologError@19$NSXMLParserParsedEntityRefMissingSemiError@25$NSXMLParserParsedEntityRefNoNameError@24$NSXMLParserPrematureDocumentEndError@5$NSXMLParserProcessingInstructionNotFinishedError@47$NSXMLParserProcessingInstructionNotStartedError@46$NSXMLParserPublicIdentifierRequiredError@71$NSXMLParserResolveExternalEntitiesAlways@3$NSXMLParserResolveExternalEntitiesNever@0$NSXMLParserResolveExternalEntitiesNoNetwork@1$NSXMLParserResolveExternalEntitiesSameOriginOnly@2$NSXMLParserSeparatorRequiredError@66$NSXMLParserSpaceRequiredError@65$NSXMLParserStandaloneValueError@78$NSXMLParserStringNotClosedError@34$NSXMLParserStringNotStartedError@33$NSXMLParserTagNameMismatchError@76$NSXMLParserURIFragmentError@92$NSXMLParserURIRequiredError@70$NSXMLParserUndeclaredEntityError@26$NSXMLParserUnfinishedTagError@77$NSXMLParserUnknownEncodingError@31$NSXMLParserUnparsedEntityError@28$NSXMLParserXMLDeclNotFinishedError@57$NSXMLParserXMLDeclNotStartedError@56$NSXMLProcessingInstructionKind@5$NSXMLTextKind@7$NSXPCConnectionCodeSigningRequirementFailure@4102$NSXPCConnectionErrorMaximum@4224$NSXPCConnectionErrorMinimum@4096$NSXPCConnectionInterrupted@4097$NSXPCConnectionInvalid@4099$NSXPCConnectionPrivileged@4096$NSXPCConnectionReplyInvalid@4101$NSYearCalendarUnit@4$NSYearForWeekOfYearCalendarUnit@16384$NS_BLOCKS_AVAILABLE@1$NS_BigEndian@2$NS_LittleEndian@1$NS_UNICHAR_IS_EIGHT_BIT@0$NS_UnknownByteOrder@0$"""
misc.update(
    {
        "NSOrderedCollectionDifferenceCalculationOptions": NewType(
            "NSOrderedCollectionDifferenceCalculationOptions", int
        ),
        "NSURLRequestNetworkServiceType": NewType(
            "NSURLRequestNetworkServiceType", int
        ),
        "NSDataBase64DecodingOptions": NewType("NSDataBase64DecodingOptions", int),
        "NSURLSessionResponseDisposition": NewType(
            "NSURLSessionResponseDisposition", int
        ),
        "NSCalculationError": NewType("NSCalculationError", int),
        "NSKeyValueObservingOptions": NewType("NSKeyValueObservingOptions", int),
        "NSJSONReadingOptions": NewType("NSJSONReadingOptions", int),
        "NSDataWritingOptions": NewType("NSDataWritingOptions", int),
        "NSGrammaticalPerson": NewType("NSGrammaticalPerson", int),
        "NSMachPortOptions": NewType("NSMachPortOptions", int),
        "NSFileCoordinatorWritingOptions": NewType(
            "NSFileCoordinatorWritingOptions", int
        ),
        "NSNumberFormatterBehavior": NewType("NSNumberFormatterBehavior", int),
        "NSDecodingFailurePolicy": NewType("NSDecodingFailurePolicy", int),
        "NSXMLNodeOptions": NewType("NSXMLNodeOptions", int),
        "NSExpressionType": NewType("NSExpressionType", int),
        "NSGrammaticalCase": NewType("NSGrammaticalCase", int),
        "NSByteCountFormatterCountStyle": NewType(
            "NSByteCountFormatterCountStyle", int
        ),
        "NSNumberFormatterStyle": NewType("NSNumberFormatterStyle", int),
        "NSCompoundPredicateType": NewType("NSCompoundPredicateType", int),
        "NSSaveOptions": NewType("NSSaveOptions", int),
        "NSRegularExpressionOptions": NewType("NSRegularExpressionOptions", int),
        "NSDateFormatterStyle": NewType("NSDateFormatterStyle", int),
        "NSRectEdge": NewType("NSRectEdge", int),
        "NSGrammaticalPartOfSpeech": NewType("NSGrammaticalPartOfSpeech", int),
        "NSURLSessionDelayedRequestDisposition": NewType(
            "NSURLSessionDelayedRequestDisposition", int
        ),
        "NSProcessInfoThermalState": NewType("NSProcessInfoThermalState", int),
        "NSStreamEvent": NewType("NSStreamEvent", int),
        "NSTestComparisonOperation": NewType("NSTestComparisonOperation", int),
        "NSFileManagerUnmountOptions": NewType("NSFileManagerUnmountOptions", int),
        "NSCalendarOptions": NewType("NSCalendarOptions", int),
        "NSPropertyListFormat": NewType("NSPropertyListFormat", int),
        "NSJSONWritingOptions": NewType("NSJSONWritingOptions", int),
        "NSURLBookmarkCreationOptions": NewType("NSURLBookmarkCreationOptions", int),
        "NSXMLParserExternalEntityResolvingPolicy": NewType(
            "NSXMLParserExternalEntityResolvingPolicy", int
        ),
        "NSURLBookmarkResolutionOptions": NewType(
            "NSURLBookmarkResolutionOptions", int
        ),
        "NSDataCompressionAlgorithm": NewType("NSDataCompressionAlgorithm", int),
        "NSTaskTerminationReason": NewType("NSTaskTerminationReason", int),
        "NSLengthFormatterUnit": NewType("NSLengthFormatterUnit", int),
        "NSURLSessionMultipathServiceType": NewType(
            "NSURLSessionMultipathServiceType", int
        ),
        "NSNotificationCoalescing": NewType("NSNotificationCoalescing", int),
        "NSNetServiceOptions": NewType("NSNetServiceOptions", int),
        "NSISO8601DateFormatOptions": NewType("NSISO8601DateFormatOptions", int),
        "NSFormattingUnitStyle": NewType("NSFormattingUnitStyle", int),
        "NSCalendarUnit": NewType("NSCalendarUnit", int),
        "NSQualityOfService": NewType("NSQualityOfService", int),
        "NSPredicateOperatorType": NewType("NSPredicateOperatorType", int),
        "NSByteCountFormatterUnits": NewType("NSByteCountFormatterUnits", int),
        "NSXMLParserError": NewType("NSXMLParserError", int),
        "NSLinguisticTaggerUnit": NewType("NSLinguisticTaggerUnit", int),
        "NSGrammaticalGender": NewType("NSGrammaticalGender", int),
        "NSTimeZoneNameStyle": NewType("NSTimeZoneNameStyle", int),
        "NSItemProviderRepresentationVisibility": NewType(
            "NSItemProviderRepresentationVisibility", int
        ),
        "NSURLRequestCachePolicy": NewType("NSURLRequestCachePolicy", int),
        "NSAttributedStringMarkdownInterpretedSyntax": NewType(
            "NSAttributedStringMarkdownInterpretedSyntax", int
        ),
        "NSTextCheckingType": NewType("NSTextCheckingType", int),
        "NSMatchingFlags": NewType("NSMatchingFlags", int),
        "NSDateComponentsFormatterZeroFormattingBehavior": NewType(
            "NSDateComponentsFormatterZeroFormattingBehavior", int
        ),
        "NSVolumeEnumerationOptions": NewType("NSVolumeEnumerationOptions", int),
        "NSGrammaticalDefiniteness": NewType("NSGrammaticalDefiniteness", int),
        "NSURLRequestAttribution": NewType("NSURLRequestAttribution", int),
        "NSRelativePosition": NewType("NSRelativePosition", int),
        "NSMatchingOptions": NewType("NSMatchingOptions", int),
        "NSRelativeDateTimeFormatterStyle": NewType(
            "NSRelativeDateTimeFormatterStyle", int
        ),
        "NSBinarySearchingOptions": NewType("NSBinarySearchingOptions", int),
        "NSKeyValueChange": NewType("NSKeyValueChange", int),
        "NSXPCConnectionOptions": NewType("NSXPCConnectionOptions", int),
        "NSPresentationIntentKind": NewType("NSPresentationIntentKind", int),
        "NSItemProviderFileOptions": NewType("NSItemProviderFileOptions", int),
        "NSURLRelationship": NewType("NSURLRelationship", int),
        "NSRelativeDateTimeFormatterUnitsStyle": NewType(
            "NSRelativeDateTimeFormatterUnitsStyle", int
        ),
        "NSURLSessionTaskState": NewType("NSURLSessionTaskState", int),
        "NSSearchPathDirectory": NewType("NSSearchPathDirectory", int),
        "NSURLSessionTaskMetricsResourceFetchType": NewType(
            "NSURLSessionTaskMetricsResourceFetchType", int
        ),
        "NSXMLNodeKind": NewType("NSXMLNodeKind", int),
        "NSStreamStatus": NewType("NSStreamStatus", int),
        "NSURLSessionWebSocketCloseCode": NewType(
            "NSURLSessionWebSocketCloseCode", int
        ),
        "NSNumberFormatterPadPosition": NewType("NSNumberFormatterPadPosition", int),
        "NSMeasurementFormatterUnitOptions": NewType(
            "NSMeasurementFormatterUnitOptions", int
        ),
        "NSFileWrapperWritingOptions": NewType("NSFileWrapperWritingOptions", int),
        "NSInlinePresentationIntent": NewType("NSInlinePresentationIntent", int),
        "NSComparisonPredicateModifier": NewType("NSComparisonPredicateModifier", int),
        "NSFileVersionAddingOptions": NewType("NSFileVersionAddingOptions", int),
        "NSOperationQueuePriority": NewType("NSOperationQueuePriority", int),
        "NSKeyValueSetMutationKind": NewType("NSKeyValueSetMutationKind", int),
        "NSFileVersionReplacingOptions": NewType("NSFileVersionReplacingOptions", int),
        "NSPostingStyle": NewType("NSPostingStyle", int),
        "NSInsertionPosition": NewType("NSInsertionPosition", int),
        "NSURLSessionAuthChallengeDisposition": NewType(
            "NSURLSessionAuthChallengeDisposition", int
        ),
        "NSFileWrapperReadingOptions": NewType("NSFileWrapperReadingOptions", int),
        "NSActivityOptions": NewType("NSActivityOptions", int),
        "NSFileCoordinatorReadingOptions": NewType(
            "NSFileCoordinatorReadingOptions", int
        ),
        "NSNumberFormatterRoundingMode": NewType("NSNumberFormatterRoundingMode", int),
        "NSUserNotificationActivationType": NewType(
            "NSUserNotificationActivationType", int
        ),
        "NSDataBase64EncodingOptions": NewType("NSDataBase64EncodingOptions", int),
        "NSPropertyListMutabilityOptions": NewType(
            "NSPropertyListMutabilityOptions", int
        ),
        "NSNetServicesError": NewType("NSNetServicesError", int),
        "NSPersonNameComponentsFormatterStyle": NewType(
            "NSPersonNameComponentsFormatterStyle", int
        ),
        "NSAttributedStringEnumerationOptions": NewType(
            "NSAttributedStringEnumerationOptions", int
        ),
        "NSEnumerationOptions": NewType("NSEnumerationOptions", int),
        "NSDistributedNotificationOptions": NewType(
            "NSDistributedNotificationOptions", int
        ),
        "NSHTTPCookieAcceptPolicy": NewType("NSHTTPCookieAcceptPolicy", int),
        "NSURLCacheStoragePolicy": NewType("NSURLCacheStoragePolicy", int),
        "NSCollectionChangeType": NewType("NSCollectionChangeType", int),
        "NSDateComponentsFormatterUnitsStyle": NewType(
            "NSDateComponentsFormatterUnitsStyle", int
        ),
        "NSStringCompareOptions": NewType("NSStringCompareOptions", int),
        "NSItemProviderErrorCode": NewType("NSItemProviderErrorCode", int),
        "NSGrammaticalDetermination": NewType("NSGrammaticalDetermination", int),
        "NSDataReadingOptions": NewType("NSDataReadingOptions", int),
        "NSURLSessionWebSocketMessageType": NewType(
            "NSURLSessionWebSocketMessageType", int
        ),
        "NSNotificationSuspensionBehavior": NewType(
            "NSNotificationSuspensionBehavior", int
        ),
        "NSSearchPathDomainMask": NewType("NSSearchPathDomainMask", int),
        "NSDataSearchOptions": NewType("NSDataSearchOptions", int),
        "NSMassFormatterUnit": NewType("NSMassFormatterUnit", int),
        "NSAppleEventSendOptions": NewType("NSAppleEventSendOptions", int),
        "NSURLHandleStatus": NewType("NSURLHandleStatus", int),
        "NSRoundingMode": NewType("NSRoundingMode", int),
        "NSStringEnumerationOptions": NewType("NSStringEnumerationOptions", int),
        "NSDirectoryEnumerationOptions": NewType("NSDirectoryEnumerationOptions", int),
        "NSAlignmentOptions": NewType("NSAlignmentOptions", int),
        "NSFormattingContext": NewType("NSFormattingContext", int),
        "NSDateIntervalFormatterStyle": NewType("NSDateIntervalFormatterStyle", int),
        "NSGrammaticalPronounType": NewType("NSGrammaticalPronounType", int),
        "NSBackgroundActivityResult": NewType("NSBackgroundActivityResult", int),
        "NSSortOptions": NewType("NSSortOptions", int),
        "NSComparisonResult": NewType("NSComparisonResult", int),
        "NSPersonNameComponentsFormatterOptions": NewType(
            "NSPersonNameComponentsFormatterOptions", int
        ),
        "NSPresentationIntentTableColumnAlignment": NewType(
            "NSPresentationIntentTableColumnAlignment", int
        ),
        "NSLinguisticTaggerOptions": NewType("NSLinguisticTaggerOptions", int),
        "NSURLErrorNetworkUnavailableReason": NewType(
            "NSURLErrorNetworkUnavailableReason", int
        ),
        "NSXMLDTDNodeKind": NewType("NSXMLDTDNodeKind", int),
        "NSWhoseSubelementIdentifier": NewType("NSWhoseSubelementIdentifier", int),
        "NSComparisonPredicateOptions": NewType("NSComparisonPredicateOptions", int),
        "NSEnergyFormatterUnit": NewType("NSEnergyFormatterUnit", int),
        "NSAttributedStringFormattingOptions": NewType(
            "NSAttributedStringFormattingOptions", int
        ),
        "NSAttributedStringMarkdownParsingFailurePolicy": NewType(
            "NSAttributedStringMarkdownParsingFailurePolicy", int
        ),
        "NSStringEncodingConversionOptions": NewType(
            "NSStringEncodingConversionOptions", int
        ),
        "NSURLSessionTaskMetricsDomainResolutionProtocol": NewType(
            "NSURLSessionTaskMetricsDomainResolutionProtocol", int
        ),
        "NSPointerFunctionsOptions": NewType("NSPointerFunctionsOptions", int),
        "NSDateFormatterBehavior": NewType("NSDateFormatterBehavior", int),
        "NSGrammaticalNumber": NewType("NSGrammaticalNumber", int),
        "NSURLCredentialPersistence": NewType("NSURLCredentialPersistence", int),
        "NSFileManagerItemReplacementOptions": NewType(
            "NSFileManagerItemReplacementOptions", int
        ),
        "NSXMLDocumentContentKind": NewType("NSXMLDocumentContentKind", int),
        "NSLocaleLanguageDirection": NewType("NSLocaleLanguageDirection", int),
    }
)
misc.update(
    {
        "NSURLFileResourceType": NewType("NSURLFileResourceType", str),
        "NSURLUbiquitousSharedItemRole": NewType("NSURLUbiquitousSharedItemRole", str),
        "NSHTTPCookiePropertyKey": NewType("NSHTTPCookiePropertyKey", str),
        "NSValueTransformerName": NewType("NSValueTransformerName", str),
        "NSStringTransform": NewType("NSStringTransform", str),
        "NSProgressUserInfoKey": NewType("NSProgressUserInfoKey", str),
        "NSURLUbiquitousSharedItemPermissions": NewType(
            "NSURLUbiquitousSharedItemPermissions", str
        ),
        "NSURLThumbnailDictionaryItem": NewType("NSURLThumbnailDictionaryItem", str),
        "NSRunLoopMode": NewType("NSRunLoopMode", str),
        "NSLocaleKey": NewType("NSLocaleKey", str),
        "NSURLFileProtectionType": NewType("NSURLFileProtectionType", str),
        "NSFileProviderServiceName": NewType("NSFileProviderServiceName", str),
        "NSTextCheckingKey": NewType("NSTextCheckingKey", str),
        "NSCalendarIdentifier": NewType("NSCalendarIdentifier", str),
        "NSProgressFileOperationKind": NewType("NSProgressFileOperationKind", str),
        "NSFileAttributeType": NewType("NSFileAttributeType", str),
        "NSStreamSOCKSProxyConfiguration": NewType(
            "NSStreamSOCKSProxyConfiguration", str
        ),
        "NSKeyValueOperator": NewType("NSKeyValueOperator", str),
        "NSLinguisticTagScheme": NewType("NSLinguisticTagScheme", str),
        "NSStreamPropertyKey": NewType("NSStreamPropertyKey", str),
        "NSFileProtectionType": NewType("NSFileProtectionType", str),
        "NSProgressKind": NewType("NSProgressKind", str),
        "NSHTTPCookieStringPolicy": NewType("NSHTTPCookieStringPolicy", str),
        "NSKeyValueChangeKey": NewType("NSKeyValueChangeKey", str),
        "NSURLUbiquitousItemDownloadingStatus": NewType(
            "NSURLUbiquitousItemDownloadingStatus", str
        ),
        "NSStreamNetworkServiceTypeValue": NewType(
            "NSStreamNetworkServiceTypeValue", str
        ),
        "NSStreamSOCKSProxyVersion": NewType("NSStreamSOCKSProxyVersion", str),
        "NSUndoManagerUserInfoKey": NewType("NSUndoManagerUserInfoKey", str),
        "NSFileAttributeKey": NewType("NSFileAttributeKey", str),
        "NSLinguisticTag": NewType("NSLinguisticTag", str),
        "NSDistributedNotificationCenterType": NewType(
            "NSDistributedNotificationCenterType", str
        ),
        "NSExceptionName": NewType("NSExceptionName", str),
        "NSNotificationName": NewType("NSNotificationName", str),
        "NSAttributedStringKey": NewType("NSAttributedStringKey", str),
        "NSURLResourceKey": NewType("NSURLResourceKey", str),
        "NSStringEncodingDetectionOptionsKey": NewType(
            "NSStringEncodingDetectionOptionsKey", str
        ),
        "NSStreamSocketSecurityLevel": NewType("NSStreamSocketSecurityLevel", str),
    }
)
misc.update(
    {
        "NSFoundationVersionNumber10_2_3": 462.0,
        "NSFoundationVersionNumber10_2_2": 462.0,
        "NSFoundationVersionNumber10_2_1": 462.0,
        "NSFoundationVersionNumber10_2_7": 462.7,
        "NSFoundationVersionNumber10_2_6": 462.0,
        "NSFoundationVersionNumber10_2_5": 462.0,
        "NSFoundationVersionNumber10_2_4": 462.0,
        "NSFoundationVersionNumber10_1_4": 425.0,
        "NSFoundationVersionNumber10_4_4_Intel": 567.23,
        "NSFoundationVersionNumber10_2_8": 462.7,
        "NSFoundationVersionNumber10_1_1": 425.0,
        "NSFoundationVersionNumber10_1_2": 425.0,
        "NSFoundationVersionNumber10_1_3": 425.0,
        "NSFoundationVersionNumber10_4_9": 567.29,
        "NSFoundationVersionNumber10_3_2": 500.3,
        "NSFoundationVersionNumber10_3_8": 500.56,
        "NSFoundationVersionNumber10_3_9": 500.58,
        "NSFoundationVersionNumber10_5_4": 677.19,
        "NSFoundationVersionNumber10_5_5": 677.21,
        "NSFoundationVersionNumber10_5_6": 677.22,
        "NSFoundationVersionNumber10_5_7": 677.24,
        "NSFoundationVersionNumber10_4_1": 567.0,
        "NSFoundationVersionNumber10_3_3": 500.54,
        "NSFoundationVersionNumber10_4_3": 567.21,
        "NSFoundationVersionNumber10_3_1": 500.0,
        "NSFoundationVersionNumber10_3_6": 500.56,
        "NSFoundationVersionNumber10_3_7": 500.56,
        "NSFoundationVersionNumber10_3_4": 500.56,
        "NSFoundationVersionNumber10_3_5": 500.56,
        "NSFoundationVersionNumber10_4_2": 567.12,
        "NSFoundationVersionNumber10_11_3": 1256.1,
        "NSFoundationVersionNumber10_5_1": 677.1,
        "NSFoundationVersionNumber10_4_5": 567.25,
        "NSFoundationVersionNumber10_6": 751.0,
        "NSFoundationVersionNumber10_7": 833.1,
        "NSFoundationVersionNumber10_4": 567.0,
        "NSFoundationVersionNumber10_5": 677.0,
        "NSFoundationVersionNumber10_2": 462.0,
        "NSFoundationVersionNumber10_4_7": 567.27,
        "NSFoundationVersionNumber10_0": 397.4,
        "NSFoundationVersionNumber10_1": 425.0,
        "NSFoundationVersionNumber10_4_6": 567.26,
        "NSFoundationVersionNumber10_8": 945.0,
        "NSFoundationVersionNumber10_3": 500.0,
        "NSFoundationVersionNumber10_4_4_PowerPC": 567.21,
        "NSFoundationVersionNumber10_4_11": 567.36,
        "NSFoundationVersionNumber10_4_10": 567.29,
        "NSFoundationVersionNumber10_9_2": 1056.13,
        "NSFoundationVersionNumber10_11_1": 1255.1,
        "NSFoundationVersionNumber10_8_4": 945.18,
        "NSFoundationVersionNumber10_10_4": 1153.2,
        "NSFoundationVersionNumber10_8_1": 945.0,
        "NSFoundationVersionNumber10_10_2": 1152.14,
        "NSFoundationVersionNumber10_10_1": 1151.16,
        "NSFoundationVersionNumber10_8_2": 945.11,
        "NSFoundationVersionNumber10_10": 1151.16,
        "NSFoundationVersionNumber10_8_3": 945.16,
        "NSTimeIntervalSince1970": 978307200.0,
        "NSFoundationVersionNumber10_6_7": 751.53,
        "NSFoundationVersionNumber10_11_2": 1256.1,
        "NSFoundationVersionNumber10_6_5": 751.42,
        "NSFoundationVersionNumber10_6_4": 751.29,
        "NSFoundationVersionNumber10_6_3": 751.21,
        "NSFoundationVersionNumber10_6_2": 751.14,
        "NSFoundationVersionNumber10_6_1": 751.0,
        "NSFoundationVersionNumber10_4_8": 567.28,
        "NSFoundationVersionNumber10_10_3": 1153.2,
        "NSFoundationVersionNumber10_5_2": 677.15,
        "NSFoundationVersionNumber10_6_8": 751.62,
        "NSFoundationVersionNumber10_6_6": 751.53,
        "NSFoundationVersionNumber10_5_3": 677.19,
        "NSFoundationVersionNumber10_7_4": 833.25,
        "NSFoundationVersionNumber10_5_8": 677.26,
        "NSFoundationVersionNumber10_7_2": 833.2,
        "NSFoundationVersionNumber10_7_3": 833.24,
        "NSFoundationVersionNumber10_7_1": 833.1,
    }
)
functions = {
    "NSSwapShort": (b"SS",),
    "NSDecimalIsNotANumber": (
        b"Z^{NSDecimal=b8b4b1b1b18[8S]}",
        "",
        {"arguments": {0: {"type_modifier": "n"}}},
    ),
    "NSSwapHostIntToBig": (b"II",),
    "NSDecimalDivide": (
        b"Q^{NSDecimal=b8b4b1b1b18[8S]}^{NSDecimal=b8b4b1b1b18[8S]}^{NSDecimal=b8b4b1b1b18[8S]}Q",
        "",
        {
            "arguments": {
                0: {"type_modifier": "o"},
                1: {"type_modifier": "n"},
                2: {"type_modifier": "n"},
            }
        },
    ),
    "NSEndMapTableEnumeration": (
        b"v^{NSMapEnumerator=QQ^v}",
        "",
        {"arguments": {0: {"type_modifier": "N"}}},
    ),
    "NSEqualRects": (
        b"Z{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "NSIntegralRect": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "NSEqualSizes": (b"Z{CGSize=dd}{CGSize=dd}",),
    "NSSwapHostLongToLittle": (b"QQ",),
    "NSSwapLittleDoubleToHost": (b"d{NSSwappedDouble=Q}",),
    "NSSizeFromCGSize": (b"{CGSize=dd}{CGSize=dd}",),
    "NSDecimalCompact": (
        b"v^{NSDecimal=b8b4b1b1b18[8S]}",
        "",
        {"arguments": {0: {"type_modifier": "N"}}},
    ),
    "NSCreateHashTable": (
        b"@{NSHashTableCallBacks=^?^?^?^?^?}Q",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "NSOpenStepRootDirectory": (b"@",),
    "NSRoundDownToMultipleOfPageSize": (b"QQ",),
    "NSMapInsertIfAbsent": (b"^v@^v^v",),
    "NSLocationInRange": (b"ZQ{_NSRange=QQ}",),
    "NSOffsetRect": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}dd",
    ),
    "NSEqualRanges": (b"Z{_NSRange=QQ}{_NSRange=QQ}",),
    "NSDecimalNormalize": (
        b"Q^{NSDecimal=b8b4b1b1b18[8S]}^{NSDecimal=b8b4b1b1b18[8S]}Q",
        "",
        {"arguments": {0: {"type_modifier": "o"}, 1: {"type_modifier": "n"}}},
    ),
    "NSFreeHashTable": (b"v@",),
    "NSHostByteOrder": (b"q",),
    "NSGetUncaughtExceptionHandler": (
        b"^?",
        "",
        {
            "retval": {
                "callable": {"retval": {"type": "v"}, "arguments": {0: {"type": "@"}}}
            }
        },
    ),
    "NSStringFromMapTable": (b"@@",),
    "NSPointFromString": (b"{CGPoint=dd}@",),
    "NSEnumerateMapTable": (b"{NSMapEnumerator=QQ^v}@",),
    "NSIsEmptyRect": (b"Z{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "NSHeight": (b"d{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "NSHomeDirectory": (b"@",),
    "NSResetMapTable": (b"v@",),
    "NSMinY": (b"d{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "NSPageSize": (b"Q",),
    "NSUserName": (b"@",),
    "NSMapInsert": (b"v@^v^v",),
    "NSDeallocateObject": (b"v@",),
    "NSDefaultMallocZone": (b"^{_NSZone=}",),
    "NSRecordAllocationEvent": (b"vi@",),
    "NSDecimalPower": (
        b"Q^{NSDecimal=b8b4b1b1b18[8S]}^{NSDecimal=b8b4b1b1b18[8S]}QQ",
        "",
        {"arguments": {0: {"type_modifier": "o"}, 1: {"type_modifier": "n"}}},
    ),
    "NSMaxRange": (b"Q{_NSRange=QQ}",),
    "NSMinX": (b"d{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "NSLogPageSize": (b"Q",),
    "NSMouseInRect": (b"Z{CGPoint=dd}{CGRect={CGPoint=dd}{CGSize=dd}}Z",),
    "NSDecimalCompare": (
        b"q^{NSDecimal=b8b4b1b1b18[8S]}^{NSDecimal=b8b4b1b1b18[8S]}",
        "",
        {"arguments": {0: {"type_modifier": "n"}, 1: {"type_modifier": "n"}}},
    ),
    "NSAllMapTableValues": (b"@@",),
    "NSProtocolFromString": (b"@@",),
    "NSPointInRect": (b"Z{CGPoint=dd}{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "NSSetZoneName": (b"v^{_NSZone=}@",),
    "CFBridgingRetain": (b"@@",),
    "NSCopyObject": (b"@@Q^{_NSZone=}", "", {"retval": {"already_cfretained": True}}),
    "NSMidY": (b"d{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "NSSwapLongLong": (b"QQ",),
    "NSDecrementExtraRefCountWasZero": (b"Z@",),
    "NSDecimalMultiply": (
        b"Q^{NSDecimal=b8b4b1b1b18[8S]}^{NSDecimal=b8b4b1b1b18[8S]}^{NSDecimal=b8b4b1b1b18[8S]}Q",
        "",
        {
            "arguments": {
                0: {"type_modifier": "o"},
                1: {"type_modifier": "n"},
                2: {"type_modifier": "n"},
            }
        },
    ),
    "NSSwapBigLongLongToHost": (b"QQ",),
    "NSShouldRetainWithZone": (b"Z@^{_NSZone=}",),
    "NSStringFromRange": (b"@{_NSRange=QQ}",),
    "NSHashGet": (b"^v@^v",),
    "NSStringFromClass": (b"@#",),
    "NSPointToCGPoint": (b"{CGPoint=dd}{CGPoint=dd}",),
    "NSUnionRect": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "NSRectToCGRect": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "NSCopyHashTableWithZone": (
        b"@@^{_NSZone=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "NSSwapBigShortToHost": (b"SS",),
    "NSSwapHostShortToBig": (b"SS",),
    "NSStringFromPoint": (b"@{CGPoint=dd}",),
    "NSWidth": (b"d{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "NSRealMemoryAvailable": (b"Q",),
    "NSNextMapEnumeratorPair": (
        b"Z^{NSMapEnumerator=QQ^v}^^v^^v",
        "",
        {
            "arguments": {
                0: {"type_modifier": "N"},
                1: {"type_modifier": "o"},
                2: {"type_modifier": "o"},
            }
        },
    ),
    "NSAllHashTableObjects": (b"@@",),
    "NSPointFromCGPoint": (b"{CGPoint=dd}{CGPoint=dd}",),
    "NSSizeToCGSize": (b"{CGSize=dd}{CGSize=dd}",),
    "NSHashInsertKnownAbsent": (b"v@^v",),
    "NSNextHashEnumeratorItem": (
        b"^v^{NSHashEnumerator=QQ^v}",
        "",
        {"arguments": {0: {"type_modifier": "N"}}},
    ),
    "NSSwapHostLongLongToLittle": (b"QQ",),
    "NSClassFromString": (b"#@",),
    "NSSwapLittleLongToHost": (b"QQ",),
    "NSMakePoint": (b"{CGPoint=dd}dd",),
    "NSSizeFromString": (b"{CGSize=dd}@",),
    "NSConvertHostFloatToSwapped": (b"{NSSwappedFloat=I}f",),
    "NSIntersectsRect": (
        b"Z{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "NSEdgeInsetsMake": (b"{NSEdgeInsets=dddd}dddd",),
    "NSIntersectionRect": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "NSDecimalAdd": (
        b"Q^{NSDecimal=b8b4b1b1b18[8S]}^{NSDecimal=b8b4b1b1b18[8S]}^{NSDecimal=b8b4b1b1b18[8S]}Q",
        "",
        {
            "arguments": {
                0: {"type_modifier": "o"},
                1: {"type_modifier": "n"},
                2: {"type_modifier": "n"},
            }
        },
    ),
    "NSCreateHashTableWithZone": (
        b"@{NSHashTableCallBacks=^?^?^?^?^?}Q^{_NSZone=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "NSSwapFloat": (b"{NSSwappedFloat=I}{NSSwappedFloat=I}",),
    "NSDecimalSubtract": (
        b"Q^{NSDecimal=b8b4b1b1b18[8S]}^{NSDecimal=b8b4b1b1b18[8S]}^{NSDecimal=b8b4b1b1b18[8S]}Q",
        "",
        {
            "arguments": {
                0: {"type_modifier": "o"},
                1: {"type_modifier": "n"},
                2: {"type_modifier": "n"},
            }
        },
    ),
    "NSSetUncaughtExceptionHandler": (
        b"v^?",
        "",
        {
            "arguments": {
                0: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"@"}},
                    },
                    "callable_retained": True,
                }
            }
        },
    ),
    "NSFreeMapTable": (b"v@",),
    "NSMapRemove": (b"v@^v",),
    "NSFullUserName": (b"@",),
    "NSSwapLittleShortToHost": (b"SS",),
    "NSSwapLong": (b"QQ",),
    "NSSwapHostLongLongToBig": (b"QQ",),
    "NSResetHashTable": (b"v@",),
    "NSStringFromRect": (b"@{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "NSSwapLittleLongLongToHost": (b"QQ",),
    "NSSwapLittleFloatToHost": (b"f{NSSwappedFloat=I}",),
    "NSSwapBigLongToHost": (b"QQ",),
    "NSCountMapTable": (b"Q@",),
    "NSHFSTypeOfFile": (b"@@",),
    "NSHashInsertIfAbsent": (b"^v@^v",),
    "NSSwapBigIntToHost": (b"II",),
    "NSRecycleZone": (b"v^{_NSZone=}",),
    "NSStringFromProtocol": (b"@@",),
    "NSFrameAddress": (b"^vQ",),
    "NSCountFrames": (b"Q",),
    "CFBridgingRelease": (b"@@",),
    "NSMapMember": (
        b"Z@^v^^v^^v",
        "",
        {"arguments": {2: {"type_modifier": "o"}, 3: {"type_modifier": "o"}}},
    ),
    "NSDivideRect": (
        b"v{CGRect={CGPoint=dd}{CGSize=dd}}^{CGRect={CGPoint=dd}{CGSize=dd}}^{CGRect={CGPoint=dd}{CGSize=dd}}dQ",
        "",
        {"arguments": {1: {"type_modifier": "o"}, 2: {"type_modifier": "o"}}},
    ),
    "NSRangeFromString": (b"{_NSRange=QQ}@",),
    "NSMapGet": (b"^v@^v",),
    "NSHashInsert": (b"v@^v",),
    "NSSwapHostIntToLittle": (b"II",),
    "NSEndHashTableEnumeration": (
        b"v^{NSHashEnumerator=QQ^v}",
        "",
        {"arguments": {0: {"type_modifier": "N"}}},
    ),
    "NSZoneName": (b"@^{_NSZone=}",),
    "NSSwapHostFloatToBig": (b"{NSSwappedFloat=I}f",),
    "NSTemporaryDirectory": (b"@",),
    "NSDecimalMultiplyByPowerOf10": (
        b"Q^{NSDecimal=b8b4b1b1b18[8S]}^{NSDecimal=b8b4b1b1b18[8S]}sQ",
        "",
        {"arguments": {0: {"type_modifier": "o"}, 1: {"type_modifier": "n"}}},
    ),
    "NSCompareHashTables": (b"Z@@",),
    "NSMakeRect": (b"{CGRect={CGPoint=dd}{CGSize=dd}}dddd",),
    "NSMakeCollectable": (b"@@",),
    "NSGetSizeAndAlignment": (
        b"^t^t^Q^Q",
        "",
        {
            "retval": {"c_array_delimited_by_null": True},
            "arguments": {
                0: {"c_array_delimited_by_null": True, "type_modifier": "n"},
                1: {"type_modifier": "o"},
                2: {"type_modifier": "o"},
            },
        },
    ),
    "NSDecimalRound": (
        b"v^{NSDecimal=b8b4b1b1b18[8S]}^{NSDecimal=b8b4b1b1b18[8S]}qQ",
        "",
        {"arguments": {0: {"type_modifier": "o"}, 1: {"type_modifier": "n"}}},
    ),
    "NSInsetRect": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}dd",
    ),
    "NSAllocateObject": (b"@#Q^{_NSZone=}",),
    "NSSwapInt": (b"II",),
    "NSUnionRange": (b"{_NSRange=QQ}{_NSRange=QQ}{_NSRange=QQ}",),
    "NSSelectorFromString": (b":@",),
    "NSStringFromHashTable": (b"@@",),
    "NSHFSTypeCodeFromFileType": (b"I@",),
    "NSSwapDouble": (b"{NSSwappedDouble=Q}{NSSwappedDouble=Q}",),
    "NSLog": (b"v@", "", {"arguments": {0: {"printf_format": True}}, "variadic": True}),
    "NSMakeSize": (b"{CGSize=dd}dd",),
    "NSSwapHostDoubleToLittle": (b"{NSSwappedDouble=Q}d",),
    "NSRectFromString": (b"{CGRect={CGPoint=dd}{CGSize=dd}}@",),
    "NSDecimalString": (
        b"@^{NSDecimal=b8b4b1b1b18[8S]}@",
        "",
        {"arguments": {0: {"type_modifier": "n"}}},
    ),
    "NSCreateZone": (b"^{_NSZone=}QQZ", "", {"retval": {"already_cfretained": True}}),
    "NSAllMapTableKeys": (b"@@",),
    "NSIncrementExtraRefCount": (b"v@",),
    "NSDecimalCopy": (
        b"v^{NSDecimal=b8b4b1b1b18[8S]}^{NSDecimal=b8b4b1b1b18[8S]}",
        "",
        {
            "retval": {"already_cfretained": True},
            "arguments": {0: {"type_modifier": "o"}, 1: {"type_modifier": "n"}},
        },
    ),
    "NSStringFromSelector": (b"@:",),
    "NSMakeRange": (b"{_NSRange=QQ}QQ",),
    "NSConvertSwappedFloatToHost": (b"f{NSSwappedFloat=I}",),
    "NSContainsRect": (
        b"Z{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "NSSwapBigDoubleToHost": (b"d{NSSwappedDouble=Q}",),
    "NSIntersectionRange": (b"{_NSRange=QQ}{_NSRange=QQ}{_NSRange=QQ}",),
    "NSSwapHostDoubleToBig": (b"{NSSwappedDouble=Q}d",),
    "NSRoundUpToMultipleOfPageSize": (b"QQ",),
    "NSConvertHostDoubleToSwapped": (b"{NSSwappedDouble=Q}d",),
    "NSSwapHostLongToBig": (b"QQ",),
    "NSMaxY": (b"d{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "NSMaxX": (b"d{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "NSCreateMapTableWithZone": (
        b"@{NSMapTableKeyCallBacks=^?^?^?^?^?^v}{NSMapTableValueCallBacks=^?^?^?}Q^{_NSZone=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "NSExtraRefCount": (b"Q@",),
    "NSRectFromCGRect": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}",
    ),
    "NSIntegralRectWithOptions": (
        b"{CGRect={CGPoint=dd}{CGSize=dd}}{CGRect={CGPoint=dd}{CGSize=dd}}Q",
    ),
    "NSStringFromSize": (b"@{CGSize=dd}",),
    "NSHomeDirectoryForUser": (b"@@",),
    "NSIsFreedObject": (b"Z@",),
    "NSSwapBigFloatToHost": (b"f{NSSwappedFloat=I}",),
    "NSConvertSwappedDoubleToHost": (b"d{NSSwappedDouble=Q}",),
    "NSMidX": (b"d{CGRect={CGPoint=dd}{CGSize=dd}}",),
    "NSReturnAddress": (b"^vQ",),
    "NSEqualPoints": (b"Z{CGPoint=dd}{CGPoint=dd}",),
    "NSCompareMapTables": (b"Z@@",),
    "NSHashRemove": (b"v@^v",),
    "NSSwapLittleIntToHost": (b"II",),
    "NSCountHashTable": (b"Q@",),
    "NSMapInsertKnownAbsent": (b"v@^v^v",),
    "NSCreateMapTable": (
        b"@{NSMapTableKeyCallBacks=^?^?^?^?^?^v}{NSMapTableValueCallBacks=^?^?^?}Q",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "NSSwapHostFloatToLittle": (b"{NSSwappedFloat=I}f",),
    "NSEdgeInsetsEqual": (b"Z{NSEdgeInsets=dddd}{NSEdgeInsets=dddd}",),
    "NSEnumerateHashTable": (b"{NSHashEnumerator=QQ^v}@",),
    "NXReadNSObjectFromCoder": (b"@@",),
    "NSCopyMapTableWithZone": (
        b"@@^{_NSZone=}",
        "",
        {"retval": {"already_cfretained": True}},
    ),
    "NSSwapHostShortToLittle": (b"SS",),
    "NSSearchPathForDirectoriesInDomains": (b"@QQZ",),
}
aliases = {
    "NSCalendarUnitYear": "kCFCalendarUnitYear",
    "NSURLErrorBadURL": "kCFURLErrorBadURL",
    "NSWeekCalendarUnit": "kCFCalendarUnitWeek",
    "NSAppleEventSendNoReply": "kAENoReply",
    "NSURLErrorCannotCreateFile": "kCFURLErrorCannotCreateFile",
    "NSWeekdayCalendarUnit": "NSCalendarUnitWeekday",
    "NSURLErrorFileIsDirectory": "kCFURLErrorFileIsDirectory",
    "NSPropertyListXMLFormat_v1_0": "kCFPropertyListXMLFormat_v1_0",
    "NSHashTableZeroingWeakMemory": "NSPointerFunctionsZeroingWeakMemory",
    "NSNumberFormatterPadBeforeSuffix": "kCFNumberFormatterPadBeforeSuffix",
    "NSCalendarUnitWeekdayOrdinal": "kCFCalendarUnitWeekdayOrdinal",
    "NSNumberFormatterDecimalStyle": "kCFNumberFormatterDecimalStyle",
    "NSMinuteCalendarUnit": "NSCalendarUnitMinute",
    "NSURLErrorRequestBodyStreamExhausted": "kCFURLErrorRequestBodyStreamExhausted",
    "NSHashTableCopyIn": "NSPointerFunctionsCopyIn",
    "NSISO8601DateFormatWithYear": "kCFISO8601DateFormatWithYear",
    "NSWeekOfMonthCalendarUnit": "NSCalendarUnitWeekOfMonth",
    "NSDateFormatterNoStyle": "kCFDateFormatterNoStyle",
    "NSTimeZoneCalendarUnit": "NSCalendarUnitTimeZone",
    "NSNumberFormatterSpellOutStyle": "kCFNumberFormatterSpellOutStyle",
    "NSNumberFormatterCurrencyPluralStyle": "kCFNumberFormatterCurrencyPluralStyle",
    "NSISO8601DateFormatWithDay": "kCFISO8601DateFormatWithDay",
    "NSURLErrorDataNotAllowed": "kCFURLErrorDataNotAllowed",
    "NSPropertyListOpenStepFormat": "kCFPropertyListOpenStepFormat",
    "NS_UnknownByteOrder": "CFByteOrderUnknown",
    "NS_FALLTHROUGH": "CF_FALLTHROUGH",
    "NSCalendarUnitWeekOfMonth": "kCFCalendarUnitWeekOfMonth",
    "NSURLErrorCallIsActive": "kCFURLErrorCallIsActive",
    "NSISO8601DateFormatWithDashSeparatorInDate": "kCFISO8601DateFormatWithDashSeparatorInDate",
    "NSCalendarUnitHour": "kCFCalendarUnitHour",
    "NSURLErrorSecureConnectionFailed": "kCFURLErrorSecureConnectionFailed",
    "NSAppleEventSendAlwaysInteract": "kAEAlwaysInteract",
    "NSRectEdgeMaxX": "NSMaxXEdge",
    "NSRectEdgeMaxY": "NSMaxYEdge",
    "NSNumberFormatterRoundCeiling": "kCFNumberFormatterRoundCeiling",
    "NSURLErrorServerCertificateUntrusted": "kCFURLErrorServerCertificateUntrusted",
    "NSAppleEventSendCanSwitchLayer": "kAECanSwitchLayer",
    "NS_STRING_ENUM": "_NS_TYPED_ENUM",
    "NSLocaleLanguageDirectionTopToBottom": "kCFLocaleLanguageDirectionTopToBottom",
    "NSNumberFormatterPadAfterPrefix": "kCFNumberFormatterPadAfterPrefix",
    "NSURLErrorNoPermissionsToReadFile": "kCFURLErrorNoPermissionsToReadFile",
    "NSQuarterCalendarUnit": "NSCalendarUnitQuarter",
    "NSNumberFormatterPercentStyle": "kCFNumberFormatterPercentStyle",
    "NSISO8601DateFormatWithFullTime": "kCFISO8601DateFormatWithFullTime",
    "NSIntegerMin": "LONG_MIN",
    "NS_TYPED_ENUM": "_NS_TYPED_ENUM",
    "NSLocaleLanguageDirectionLeftToRight": "kCFLocaleLanguageDirectionLeftToRight",
    "NSNumberFormatterPadAfterSuffix": "kCFNumberFormatterPadAfterSuffix",
    "NSURLErrorClientCertificateRequired": "kCFURLErrorClientCertificateRequired",
    "NSSecondCalendarUnit": "NSCalendarUnitSecond",
    "NSURLErrorCannotConnectToHost": "kCFURLErrorCannotConnectToHost",
    "NSNumberFormatterOrdinalStyle": "kCFNumberFormatterOrdinalStyle",
    "NSURLErrorZeroByteResource": "kCFURLErrorZeroByteResource",
    "NSMonthCalendarUnit": "NSCalendarUnitMonth",
    "NSNumberFormatterNoStyle": "kCFNumberFormatterNoStyle",
    "NSHashTableWeakMemory": "NSPointerFunctionsWeakMemory",
    "NSAppleEventSendDontExecute": "kAEDontExecute",
    "NS_NONATOMIC_IOSONLY": "atomic",
    "NSURLErrorClientCertificateRejected": "kCFURLErrorClientCertificateRejected",
    "NSURLErrorUserCancelledAuthentication": "kCFURLErrorUserCancelledAuthentication",
    "NSCalendarUnitWeekOfYear": "kCFCalendarUnitWeekOfYear",
    "NSDateFormatterLongStyle": "kCFDateFormatterLongStyle",
    "NSURLErrorCannotLoadFromNetwork": "kCFURLErrorCannotLoadFromNetwork",
    "NSWeekdayOrdinalCalendarUnit": "NSCalendarUnitWeekdayOrdinal",
    "NSURLErrorResourceUnavailable": "kCFURLErrorResourceUnavailable",
    "NSURLErrorNetworkConnectionLost": "kCFURLErrorNetworkConnectionLost",
    "NS_LittleEndian": "CFByteOrderLittleEndian",
    "NSEraCalendarUnit": "NSCalendarUnitEra",
    "NSISO8601DateFormatWithColonSeparatorInTime": "kCFISO8601DateFormatWithColonSeparatorInTime",
    "NSPropertyListMutableContainers": "kCFPropertyListMutableContainers",
    "NSHashTableObjectPointerPersonality": "NSPointerFunctionsObjectPointerPersonality",
    "NS_VOIDRETURN": "return",
    "NS_REFINED_FOR_SWIFT": "CF_REFINED_FOR_SWIFT",
    "NS_EXTENSIBLE_STRING_ENUM": "_NS_TYPED_EXTENSIBLE_ENUM",
    "NSOperationQualityOfServiceUtility": "NSQualityOfServiceUtility",
    "NSNumberFormatterCurrencyAccountingStyle": "kCFNumberFormatterCurrencyAccountingStyle",
    "NSPropertyListBinaryFormat_v1_0": "kCFPropertyListBinaryFormat_v1_0",
    "NSURLErrorDNSLookupFailed": "kCFURLErrorDNSLookupFailed",
    "NSYearCalendarUnit": "NSCalendarUnitYear",
    "NS_NONATOMIC_IPHONEONLY": "NS_NONATOMIC_IOSONLY",
    "NSURLErrorRedirectToNonExistentLocation": "kCFURLErrorRedirectToNonExistentLocation",
    "NSURLErrorNotConnectedToInternet": "kCFURLErrorNotConnectedToInternet",
    "NSDataReadingMapped": "NSDataReadingMappedIfSafe",
    "_NS_TYPED_EXTENSIBLE_ENUM": "_CF_TYPED_EXTENSIBLE_ENUM",
    "NSURLErrorCannotDecodeRawData": "kCFURLErrorCannotDecodeRawData",
    "NSMapTableObjectPointerPersonality": "NSPointerFunctionsObjectPointerPersonality",
    "NSURLErrorCannotMoveFile": "kCFURLErrorCannotMoveFile",
    "NSPropertyListMutableContainersAndLeaves": "kCFPropertyListMutableContainersAndLeaves",
    "NSURLErrorCancelled": "kCFURLErrorCancelled",
    "NSRectEdgeMinX": "NSMinXEdge",
    "NSRectEdgeMinY": "NSMinYEdge",
    "NSPropertyListImmutable": "kCFPropertyListImmutable",
    "NSCalendarUnitYearForWeekOfYear": "kCFCalendarUnitYearForWeekOfYear",
    "NSCalendarCalendarUnit": "NSCalendarUnitCalendar",
    "NSURLErrorDownloadDecodingFailedMidStream": "kCFURLErrorDownloadDecodingFailedMidStream",
    "NSURLErrorTimedOut": "kCFURLErrorTimedOut",
    "NSISO8601DateFormatWithFullDate": "kCFISO8601DateFormatWithFullDate",
    "NSNumberFormatterRoundFloor": "kCFNumberFormatterRoundFloor",
    "NSRect": "CGRect",
    "NSOperationQualityOfServiceUserInitiated": "NSQualityOfServiceUserInitiated",
    "NSCalendarUnitWeekday": "kCFCalendarUnitWeekday",
    "NS_BigEndian": "CFByteOrderBigEndian",
    "NSMapTableZeroingWeakMemory": "NSPointerFunctionsZeroingWeakMemory",
    "NS_UNAVAILABLE": "UNAVAILABLE_ATTRIBUTE",
    "NSOperationQualityOfServiceUserInteractive": "NSQualityOfServiceUserInteractive",
    "NSURLErrorCannotDecodeContentData": "kCFURLErrorCannotDecodeContentData",
    "NSUTF16StringEncoding": "NSUnicodeStringEncoding",
    "NSNumberFormatterRoundDown": "kCFNumberFormatterRoundDown",
    "NSURLErrorHTTPTooManyRedirects": "kCFURLErrorHTTPTooManyRedirects",
    "NSISO8601DateFormatWithSpaceBetweenDateAndTime": "kCFISO8601DateFormatWithSpaceBetweenDateAndTime",
    "NSNumberFormatterRoundHalfUp": "kCFNumberFormatterRoundHalfUp",
    "NSISO8601DateFormatWithInternetDateTime": "kCFISO8601DateFormatWithInternetDateTime",
    "NSCalendarUnitMinute": "kCFCalendarUnitMinute",
    "NSPoint": "CGPoint",
    "NSISO8601DateFormatWithMonth": "kCFISO8601DateFormatWithMonth",
    "NSNumberFormatterScientificStyle": "kCFNumberFormatterScientificStyle",
    "NSURLErrorInternationalRoamingOff": "kCFURLErrorInternationalRoamingOff",
    "NSLocaleLanguageDirectionUnknown": "kCFLocaleLanguageDirectionUnknown",
    "NSCalendarUnitSecond": "kCFCalendarUnitSecond",
    "NSURLErrorCannotParseResponse": "kCFURLErrorCannotParseResponse",
    "NSAppleEventSendDontRecord": "kAEDontRecord",
    "NSOperationQualityOfServiceBackground": "NSQualityOfServiceBackground",
    "NSAppleEventSendWaitForReply": "kAEWaitReply",
    "NSMapTableCopyIn": "NSPointerFunctionsCopyIn",
    "NSCalendarUnitMonth": "kCFCalendarUnitMonth",
    "NSURLErrorCannotWriteToFile": "kCFURLErrorCannotWriteToFile",
    "NSURLErrorServerCertificateHasBadDate": "kCFURLErrorServerCertificateHasBadDate",
    "NSURLErrorUserAuthenticationRequired": "kCFURLErrorUserAuthenticationRequired",
    "NSURLErrorDataLengthExceedsMaximum": "kCFURLErrorDataLengthExceedsMaximum",
    "NSCalendarUnitEra": "kCFCalendarUnitEra",
    "NSDateFormatterFullStyle": "kCFDateFormatterFullStyle",
    "NSAppleEventSendNeverInteract": "kAENeverInteract",
    "NSISO8601DateFormatWithColonSeparatorInTimeZone": "kCFISO8601DateFormatWithColonSeparatorInTimeZone",
    "NSURLErrorCannotOpenFile": "kCFURLErrorCannotOpenFile",
    "_NS_TYPED_ENUM": "_CF_TYPED_ENUM",
    "NSDateFormatterShortStyle": "kCFDateFormatterShortStyle",
    "NSDecimalNoScale": "SHRT_MAX",
    "NSLocaleLanguageDirectionRightToLeft": "kCFLocaleLanguageDirectionRightToLeft",
    "NSAppleEventSendCanInteract": "kAECanInteract",
    "NSISO8601DateFormatWithTime": "kCFISO8601DateFormatWithTime",
    "NSNumberFormatterCurrencyISOCodeStyle": "kCFNumberFormatterCurrencyISOCodeStyle",
    "NSCalendarUnitQuarter": "kCFCalendarUnitQuarter",
    "NSJSONReadingAllowFragments": "NSJSONReadingFragmentsAllowed",
    "NSNumberFormatterCurrencyStyle": "kCFNumberFormatterCurrencyStyle",
    "NSWeekOfYearCalendarUnit": "NSCalendarUnitWeekOfYear",
    "NS_WARN_UNUSED_RESULT": "CF_WARN_UNUSED_RESULT",
    "NSURLErrorServerCertificateNotYetValid": "kCFURLErrorServerCertificateNotYetValid",
    "NSMapTableWeakMemory": "NSPointerFunctionsWeakMemory",
    "NSURLErrorCannotRemoveFile": "kCFURLErrorCannotRemoveFile",
    "NSWrapCalendarComponents": "NSCalendarWrapComponents",
    "NSURLErrorFileDoesNotExist": "kCFURLErrorFileDoesNotExist",
    "NSLocaleLanguageDirectionBottomToTop": "kCFLocaleLanguageDirectionBottomToTop",
    "NSUncachedRead": "NSDataReadingUncached",
    "NSIntegerMax": "LONG_MAX",
    "NSDateFormatterMediumStyle": "kCFDateFormatterMediumStyle",
    "NSCalendarUnitDayOfYear": "kCFCalendarUnitDayOfYear",
    "NSURLErrorUnsupportedURL": "kCFURLErrorUnsupportedURL",
    "NSNumberFormatterRoundHalfEven": "kCFNumberFormatterRoundHalfEven",
    "NSISO8601DateFormatWithWeekOfYear": "kCFISO8601DateFormatWithWeekOfYear",
    "NSDayCalendarUnit": "NSCalendarUnitDay",
    "NSISO8601DateFormatWithFractionalSeconds": "kCFISO8601DateFormatWithFractionalSeconds",
    "NSYearForWeekOfYearCalendarUnit": "NSCalendarUnitYearForWeekOfYear",
    "NSSize": "CGSize",
    "NS_TYPED_EXTENSIBLE_ENUM": "_NS_TYPED_EXTENSIBLE_ENUM",
    "NSNumberFormatterPadBeforePrefix": "kCFNumberFormatterPadBeforePrefix",
    "NSUndefinedDateComponent": "NSDateComponentUndefined",
    "NSAppleEventSendDontAnnotate": "kAEDoNotAutomaticallyAddAnnotationsToEvent",
    "NSURLErrorServerCertificateHasUnknownRoot": "kCFURLErrorServerCertificateHasUnknownRoot",
    "NSURLErrorBadServerResponse": "kCFURLErrorBadServerResponse",
    "NSMappedRead": "NSDataReadingMapped",
    "NSUIntegerMax": "ULONG_MAX",
    "NSHourCalendarUnit": "NSCalendarUnitHour",
    "NSAppleEventSendQueueReply": "kAEQueueReply",
    "NS_NOESCAPE": "CF_NOESCAPE",
    "NSURLRequestReloadIgnoringCacheData": "NSURLRequestReloadIgnoringLocalCacheData",
    "NSURLErrorCannotFindHost": "kCFURLErrorCannotFindHost",
    "NSNumberFormatterRoundUp": "kCFNumberFormatterRoundUp",
    "NSISO8601DateFormatWithTimeZone": "kCFISO8601DateFormatWithTimeZone",
    "NS_SWIFT_BRIDGED_TYPEDEF": "CF_SWIFT_BRIDGED_TYPEDEF",
    "NSURLErrorCannotCloseFile": "kCFURLErrorCannotCloseFile",
    "NSCalendarUnitDay": "kCFCalendarUnitDay",
    "NSOperationQualityOfService": "NSQualityOfService",
    "NSURLErrorDownloadDecodingFailedToComplete": "kCFURLErrorDownloadDecodingFailedToComplete",
    "NSNumberFormatterRoundHalfDown": "kCFNumberFormatterRoundHalfDown",
    "NSAtomicWrite": "NSDataWritingAtomic",
}
misc.update(
    {
        "NSAppleEventManagerSuspensionID": objc.createOpaquePointerType(
            "NSAppleEventManagerSuspensionID", b"^{__NSAppleEventManagerSuspension=}"
        ),
        "NSZonePtr": objc.createOpaquePointerType("NSZonePtr", b"^{_NSZone=}"),
    }
)
r = objc.registerMetaDataForSelector
objc._updatingMetadata(True)
try:
    r(
        b"NSAffineTransform",
        b"setTransformStruct:",
        {"arguments": {2: {"type": "{NSAffineTransformStruct=dddddd}"}}},
    )
    r(
        b"NSAffineTransform",
        b"transformPoint:",
        {
            "retval": {"type": "{CGPoint=dd}"},
            "arguments": {2: {"type": "{CGPoint=dd}"}},
        },
    )
    r(
        b"NSAffineTransform",
        b"transformSize:",
        {"retval": {"type": "{CGSize=dd}"}, "arguments": {2: {"type": "{CGSize=dd}"}}},
    )
    r(
        b"NSAffineTransform",
        b"transformStruct",
        {"retval": {"type": "{NSAffineTransformStruct=dddddd}"}},
    )
    r(
        b"NSAppleEventDescriptor",
        b"aeDesc",
        {"retval": {"type": "r^{AEDesc=I^^{OpaqueAEDataStorageType}}"}},
    )
    r(b"NSAppleEventDescriptor", b"booleanValue", {"retval": {"type": "Z"}})
    r(
        b"NSAppleEventDescriptor",
        b"descriptorWithBoolean:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSAppleEventDescriptor",
        b"descriptorWithDescriptorType:bytes:length:",
        {"arguments": {3: {"type_modifier": b"n", "c_array_length_in_arg": 4}}},
    )
    r(
        b"NSAppleEventDescriptor",
        b"dispatchRawAppleEvent:withRawReply:handlerRefCon:",
        {"retval": {"type": "s"}, "arguments": {4: {"type": "l"}}},
    )
    r(
        b"NSAppleEventDescriptor",
        b"initWithAEDescNoCopy:",
        {
            "arguments": {
                2: {
                    "type": "r^{AEDesc=I^^{OpaqueAEDataStorageType}}",
                    "type_modifier": b"n",
                }
            }
        },
    )
    r(
        b"NSAppleEventDescriptor",
        b"initWithDescriptorType:bytes:length:",
        {"arguments": {3: {"type_modifier": b"n", "c_array_length_in_arg": 4}}},
    )
    r(b"NSAppleEventDescriptor", b"isRecordDescriptor", {"retval": {"type": "Z"}})
    r(
        b"NSAppleEventDescriptor",
        b"sendEventWithOptions:timeout:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSAppleEventDescriptor",
        b"setEventHandler:andSelector:forEventClass:andEventID:",
        {"arguments": {3: {"sel_of_type": b"v@:@@"}}},
    )
    r(
        b"NSAppleEventManager",
        b"dispatchRawAppleEvent:withRawReply:handlerRefCon:",
        {
            "arguments": {
                2: {
                    "type": "r^{AEDesc=I^^{OpaqueAEDataStorageType}}",
                    "type_modifier": b"n",
                },
                3: {
                    "type": "r^{AEDesc=I^^{OpaqueAEDataStorageType}}",
                    "type_modifier": b"o",
                },
            }
        },
    )
    r(
        b"NSAppleEventManager",
        b"setEventHandler:andSelector:forEventClass:andEventID:",
        {"arguments": {3: {"sel_of_type": b"v@:@@"}}},
    )
    r(
        b"NSAppleScript",
        b"compileAndReturnError:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"NSAppleScript",
        b"executeAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"NSAppleScript",
        b"executeAppleEvent:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSAppleScript",
        b"initWithContentsOfURL:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"NSAppleScript", b"isCompiled", {"retval": {"type": "Z"}})
    r(b"NSArchiver", b"archiveRootObject:toFile:", {"retval": {"type": "Z"}})
    r(
        b"NSArray",
        b"addObserver:forKeyPath:options:context:",
        {"arguments": {5: {"type": "^v"}}},
    )
    r(
        b"NSArray",
        b"addObserver:toObjectsAtIndexes:forKeyPath:options:context:",
        {"arguments": {6: {"type": "^v"}}},
    )
    r(
        b"NSArray",
        b"arrayWithContentsOfURL:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSArray",
        b"arrayWithObjects:",
        {"c_array_delimited_by_null": True, "variadic": True},
    )
    r(
        b"NSArray",
        b"arrayWithObjects:count:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(b"NSArray", b"containsObject:", {"retval": {"type": "Z"}})
    r(b"NSArray", b"context:", {"arguments": {2: {"type": "^v"}}})
    r(b"NSArray", b"context:hint:", {"arguments": {2: {"type": "^v"}}})
    r(
        b"NSArray",
        b"differenceFromArray:withOptions:usingEquivalenceTest:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"b"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSArray",
        b"enumerateObjectsAtIndexes:options:usingBlock:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSArray",
        b"enumerateObjectsUsingBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSArray",
        b"enumerateObjectsWithOptions:usingBlock:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSArray",
        b"getObjects:",
        {
            "arguments": {2: {"type": "^@"}},
            "suggestion": "convert to Python list instead",
        },
    )
    r(
        b"NSArray",
        b"getObjects:range:",
        {
            "retval": {"type": "v"},
            "arguments": {
                2: {"type_modifier": b"o", "c_array_length_in_arg": 3},
                3: {"type": "{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSArray",
        b"indexOfObject:inRange:",
        {"arguments": {3: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSArray",
        b"indexOfObject:inSortedRange:options:usingComparator:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"l"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSArray",
        b"indexOfObjectAtIndexes:options:passingTest:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSArray",
        b"indexOfObjectIdenticalTo:inRange:",
        {"arguments": {3: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSArray",
        b"indexOfObjectPassingTest:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSArray",
        b"indexOfObjectWithOptions:passingTest:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSArray",
        b"indexesOfObjectsAtIndexes:options:passingTest:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSArray",
        b"indexesOfObjectsPassingTest:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSArray",
        b"indexesOfObjectsWithOptions:passingTest:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(b"NSArray", b"initWithArray:copyItems:", {"arguments": {3: {"type": "Z"}}})
    r(
        b"NSArray",
        b"initWithContentsOfURL:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSArray",
        b"initWithObjects:",
        {"c_array_delimited_by_null": True, "variadic": True},
    )
    r(
        b"NSArray",
        b"initWithObjects:count:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(b"NSArray", b"isEqualToArray:", {"retval": {"type": "Z"}})
    r(
        b"NSArray",
        b"makeObjectsPerformSelector:",
        {"arguments": {2: {"sel_of_type": b"v@:"}}},
    )
    r(
        b"NSArray",
        b"makeObjectsPerformSelector:withObject:",
        {"arguments": {2: {"sel_of_type": b"v@:@"}}},
    )
    r(
        b"NSArray",
        b"sortedArrayUsingComparator:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"l"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSArray",
        b"sortedArrayUsingFunction:context:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"l"},
                        "arguments": {
                            0: {"type": b"@"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "callable_retained": False,
                },
                3: {"type": "@"},
            }
        },
    )
    r(
        b"NSArray",
        b"sortedArrayUsingFunction:context:hint:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"l"},
                        "arguments": {
                            0: {"type": b"@"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "callable_retained": False,
                },
                3: {"type": "@"},
            }
        },
    )
    r(
        b"NSArray",
        b"sortedArrayUsingSelector:",
        {"arguments": {2: {"sel_of_type": b"i@:@"}}},
    )
    r(
        b"NSArray",
        b"sortedArrayWithOptions:usingComparator:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"l"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"NSArray", b"subarrayWithRange:", {"arguments": {2: {"type": "{_NSRange=QQ}"}}})
    r(
        b"NSArray",
        b"writeToFile:atomically:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSArray",
        b"writeToURL:atomically:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSArray",
        b"writeToURL:error:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSAssertionHandler",
        b"handleFailureInFunction:file:lineNumber:description:",
        {"arguments": {5: {"printf_format": True, "type": "@"}}, "variadic": True},
    )
    r(
        b"NSAssertionHandler",
        b"handleFailureInMethod:object:file:lineNumber:description:",
        {
            "arguments": {2: {"type": ":"}, 6: {"printf_format": True, "type": "@"}},
            "variadic": True,
        },
    )
    r(
        b"NSAttributedString",
        b"attribute:atIndex:effectiveRange:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSAttributedString",
        b"attribute:atIndex:longestEffectiveRange:inRange:",
        {"arguments": {4: {"type_modifier": b"o"}, 5: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSAttributedString",
        b"attributedSubstringFromRange:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSAttributedString",
        b"attributesAtIndex:effectiveRange:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSAttributedString",
        b"attributesAtIndex:longestEffectiveRange:inRange:",
        {"arguments": {3: {"type_modifier": b"o"}, 4: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSAttributedString",
        b"enumerateAttribute:inRange:options:usingBlock:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"{_NSRange=QQ}"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSAttributedString",
        b"enumerateAttributesInRange:options:usingBlock:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"{_NSRange=QQ}"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSAttributedString",
        b"initWithContentsOfMarkdownFileAtURL:options:baseURL:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"NSAttributedString",
        b"initWithFormat:options:locale:",
        {"arguments": {2: {"printf_format": True}}, "variadic": True},
    )
    r(
        b"NSAttributedString",
        b"initWithFormat:options:locale:context:",
        {"variadic": True},
    )
    r(
        b"NSAttributedString",
        b"initWithFormat:options:locale:context:arguments:",
        {"suggestion": "Use -initWithFormat:options:locale:context: instead"},
    )
    r(
        b"NSAttributedString",
        b"initWithMarkdown:options:baseURL:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"NSAttributedString",
        b"initWithMarkdownString:options:baseURL:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(b"NSAttributedString", b"isEqualToAttributedString:", {"retval": {"type": "Z"}})
    r(
        b"NSAttributedString",
        b"localizedAttributedStringWithFormat:",
        {"arguments": {2: {"printf_format": True}}, "variadic": True},
    )
    r(
        b"NSAttributedString",
        b"localizedAttributedStringWithFormat:context:",
        {"variadic": True},
    )
    r(
        b"NSAttributedString",
        b"localizedAttributedStringWithFormat:options:",
        {"arguments": {2: {"printf_format": True}}, "variadic": True},
    )
    r(
        b"NSAttributedString",
        b"localizedAttributedStringWithFormat:options:context:",
        {"variadic": True},
    )
    r(
        b"NSAttributedStringMarkdownParsingOptions",
        b"allowsExtendedAttributes",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"NSAttributedStringMarkdownParsingOptions",
        b"appliesSourcePositionAttributes",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"NSAttributedStringMarkdownParsingOptions",
        b"setAllowsExtendedAttributes:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSAttributedStringMarkdownParsingOptions",
        b"setAppliesSourcePositionAttributes:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSAutoreleasePool",
        b"enableFreedObjectCheck:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"NSAutoreleasePool", b"enableRelease:", {"arguments": {2: {"type": "Z"}}})
    r(b"NSBackgroundActivityScheduler", b"repeats", {"retval": {"type": b"Z"}})
    r(
        b"NSBackgroundActivityScheduler",
        b"scheduleWithBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"q"}},
                    }
                }
            }
        },
    )
    r(
        b"NSBackgroundActivityScheduler",
        b"setRepeats:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"NSBackgroundActivityScheduler", b"shouldDefer", {"retval": {"type": b"Z"}})
    r(
        b"NSBlockOperation",
        b"addExecutionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSBlockOperation",
        b"blockOperationWithBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(b"NSBundle", b"isLoaded", {"retval": {"type": "Z"}})
    r(b"NSBundle", b"load", {"retval": {"type": "Z"}})
    r(
        b"NSBundle",
        b"loadAndReturnError:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"NSBundle",
        b"preflightAndReturnError:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(b"NSBundle", b"unload", {"retval": {"type": "Z"}})
    r(
        b"NSBundleResourceRequest",
        b"beginAccessingResourcesWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSBundleResourceRequest",
        b"conditionallyBeginAccessingResourcesWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"NSByteCountFormatter",
        b"allowsNonnumericFormatting",
        {"retval": {"type": b"Z"}},
    )
    r(b"NSByteCountFormatter", b"includesActualByteCount", {"retval": {"type": b"Z"}})
    r(b"NSByteCountFormatter", b"includesCount", {"retval": {"type": b"Z"}})
    r(b"NSByteCountFormatter", b"includesUnit", {"retval": {"type": b"Z"}})
    r(b"NSByteCountFormatter", b"isAdaptive", {"retval": {"type": b"Z"}})
    r(b"NSByteCountFormatter", b"setAdaptive:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"NSByteCountFormatter",
        b"setAllowsNonnumericFormatting:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSByteCountFormatter",
        b"setIncludesActualByteCount:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"NSByteCountFormatter", b"setIncludesCount:", {"arguments": {2: {"type": b"Z"}}})
    r(b"NSByteCountFormatter", b"setIncludesUnit:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"NSByteCountFormatter",
        b"setZeroPadsFractionDigits:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"NSByteCountFormatter", b"zeroPadsFractionDigits", {"retval": {"type": b"Z"}})
    r(b"NSCache", b"evictsObjectsWithDiscardedContent", {"retval": {"type": "Z"}})
    r(
        b"NSCache",
        b"setEvictsObjectsWithDiscardedContent:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"NSCalendar", b"date:matchesComponents:", {"retval": {"type": b"Z"}})
    r(
        b"NSCalendar",
        b"enumerateDatesStartingAfterDate:matchingComponents:options:usingBlock:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Z"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSCalendar",
        b"isDate:equalToDate:toUnitGranularity:",
        {"retval": {"type": b"Z"}},
    )
    r(b"NSCalendar", b"isDate:inSameDayAsDate:", {"retval": {"type": b"Z"}})
    r(b"NSCalendar", b"isDateInToday:", {"retval": {"type": b"Z"}})
    r(b"NSCalendar", b"isDateInTomorrow:", {"retval": {"type": b"Z"}})
    r(b"NSCalendar", b"isDateInWeekend:", {"retval": {"type": b"Z"}})
    r(b"NSCalendar", b"isDateInYesterday:", {"retval": {"type": b"Z"}})
    r(b"NSCalendar", b"maximumRangeOfUnit:", {"retval": {"type": "{_NSRange=QQ}"}})
    r(b"NSCalendar", b"minimumRangeOfUnit:", {"retval": {"type": "{_NSRange=QQ}"}})
    r(
        b"NSCalendar",
        b"nextWeekendStartDate:interval:options:afterDate:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"NSCalendar",
        b"rangeOfUnit:inUnit:forDate:",
        {"retval": {"type": "{_NSRange=QQ}"}},
    )
    r(
        b"NSCalendar",
        b"rangeOfUnit:startDate:interval:forDate:",
        {
            "retval": {"type": "Z"},
            "arguments": {3: {"type_modifier": b"o"}, 4: {"type_modifier": b"o"}},
        },
    )
    r(
        b"NSCalendar",
        b"rangeOfWeekendStartDate:interval:containingDate:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"NSCalendarDate",
        b"years:months:days:hours:minutes:seconds:sinceDate:",
        {
            "retval": {"type": "v"},
            "arguments": {
                2: {"type_modifier": b"o"},
                3: {"type_modifier": b"o"},
                4: {"type_modifier": b"o"},
                5: {"type_modifier": b"o"},
                6: {"type_modifier": b"o"},
                7: {"type_modifier": b"o"},
                8: {"type": "@"},
            },
        },
    )
    r(
        b"NSCharacterSet",
        b"characterIsMember:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": "S"}}},
    )
    r(
        b"NSCharacterSet",
        b"characterSetWithRange:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(b"NSCharacterSet", b"hasMemberInPlane:", {"retval": {"type": "Z"}})
    r(b"NSCharacterSet", b"isSupersetOfSet:", {"retval": {"type": "Z"}})
    r(b"NSCharacterSet", b"longCharacterIsMember:", {"retval": {"type": "Z"}})
    r(b"NSCoder", b"allowsKeyedCoding", {"retval": {"type": "Z"}})
    r(b"NSCoder", b"containsValueForKey:", {"retval": {"type": "Z"}})
    r(
        b"NSCoder",
        b"decodeArrayOfObjCType:count:at:",
        {
            "arguments": {
                2: {
                    "c_array_delimited_by_null": True,
                    "type": "^t",
                    "type_modifier": b"n",
                },
                4: {"type_modifier": b"o", "c_array_of_variable_length": True},
            }
        },
    )
    r(b"NSCoder", b"decodeBoolForKey:", {"retval": {"type": "Z"}})
    r(
        b"NSCoder",
        b"decodeBytesForKey:returnedLength:",
        {
            "retval": {
                "c_array_delimited_by_null": True,
                "type": "^v",
                "c_array_length_in_arg": 3,
            },
            "arguments": {3: {"type_modifier": b"o"}},
        },
    )
    r(
        b"NSCoder",
        b"decodeBytesWithReturnedLength:",
        {
            "retval": {"c_array_length_in_arg": 2},
            "arguments": {2: {"type_modifier": b"o"}},
        },
    )
    r(b"NSCoder", b"decodePoint", {"retval": {"type": "{CGPoint=dd}"}})
    r(
        b"NSCoder",
        b"decodePointForKey:",
        {"retval": {"type": "{CGPoint=dd}"}, "arguments": {2: {"type": "@"}}},
    )
    r(
        b"NSCoder",
        b"decodeRect",
        {"retval": {"type": "{CGRect={CGPoint=dd}{CGSize=dd}}"}},
    )
    r(
        b"NSCoder",
        b"decodeRectForKey:",
        {
            "retval": {"type": "{CGRect={CGPoint=dd}{CGSize=dd}}"},
            "arguments": {2: {"type": "@"}},
        },
    )
    r(b"NSCoder", b"decodeSize", {"retval": {"type": "{CGSize=dd}"}})
    r(
        b"NSCoder",
        b"decodeSizeForKey:",
        {"retval": {"type": "{CGSize=dd}"}, "arguments": {2: {"type": "@"}}},
    )
    r(
        b"NSCoder",
        b"decodeTopLevelObjectAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"NSCoder",
        b"decodeTopLevelObjectForKey:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSCoder",
        b"decodeTopLevelObjectOfClass:forKey:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSCoder",
        b"decodeTopLevelObjectOfClasses:forKey:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSCoder",
        b"decodeValueOfObjCType:at:",
        {
            "arguments": {
                2: {
                    "c_array_delimited_by_null": True,
                    "type": "^t",
                    "type_modifier": b"n",
                },
                3: {"type": "^v", "c_array_of_variable_length": True},
            }
        },
    )
    r(
        b"NSCoder",
        b"decodeValuesOfObjCTypes:",
        {
            "arguments": {
                2: {
                    "c_array_delimited_by_null": True,
                    "type": "^t",
                    "type_modifier": b"n",
                }
            },
            "variadic": True,
        },
    )
    r(
        b"NSCoder",
        b"encodeArrayOfObjCType:count:at:",
        {
            "arguments": {
                2: {
                    "c_array_delimited_by_null": True,
                    "type": "^t",
                    "type_modifier": b"n",
                },
                4: {
                    "type": "^v",
                    "type_modifier": b"n",
                    "c_array_of_variable_length": True,
                },
            }
        },
    )
    r(b"NSCoder", b"encodeBool:forKey:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"NSCoder",
        b"encodeBytes:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSCoder",
        b"encodeBytes:length:forKey:",
        {
            "arguments": {
                2: {
                    "c_array_delimited_by_null": True,
                    "type": "^v",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(b"NSCoder", b"encodePoint:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(b"NSCoder", b"encodePoint:forKey:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(
        b"NSCoder",
        b"encodeRect:",
        {"arguments": {2: {"type": "{CGRect={CGPoint=dd}{CGSize=dd}}"}}},
    )
    r(
        b"NSCoder",
        b"encodeRect:forKey:",
        {"arguments": {2: {"type": "{CGRect={CGPoint=dd}{CGSize=dd}}"}}},
    )
    r(b"NSCoder", b"encodeSize:", {"arguments": {2: {"type": "{CGSize=dd}"}}})
    r(b"NSCoder", b"encodeSize:forKey:", {"arguments": {2: {"type": "{CGSize=dd}"}}})
    r(
        b"NSCoder",
        b"encodeValueOfObjCType:at:",
        {
            "arguments": {
                2: {
                    "c_array_delimited_by_null": True,
                    "type": "^t",
                    "type_modifier": b"n",
                },
                3: {
                    "type": "^v",
                    "type_modifier": b"n",
                    "c_array_of_variable_length": True,
                },
            }
        },
    )
    r(
        b"NSCoder",
        b"encodeValuesOfObjCTypes:",
        {
            "arguments": {
                2: {
                    "c_array_delimited_by_null": True,
                    "type": "^t",
                    "type_modifier": b"n",
                }
            },
            "variadic": True,
        },
    )
    r(b"NSCoder", b"requiresSecureCoding", {"retval": {"type": b"Z"}})
    r(b"NSComparisonPredicate", b"customSelector", {"retval": {"sel_of_type": b"Z@:@"}})
    r(
        b"NSComparisonPredicate",
        b"initWithLeftExpression:rightExpression:customSelector:",
        {"arguments": {4: {"sel_of_type": b"Z@:@"}}},
    )
    r(
        b"NSComparisonPredicate",
        b"predicateWithLeftExpression:rightExpression:customSelector:",
        {"arguments": {4: {"sel_of_type": b"Z@:@"}}},
    )
    r(b"NSCondition", b"waitUntilDate:", {"retval": {"type": "Z"}})
    r(b"NSConditionLock", b"lockBeforeDate:", {"retval": {"type": "Z"}})
    r(b"NSConditionLock", b"lockWhenCondition:beforeDate:", {"retval": {"type": "Z"}})
    r(b"NSConditionLock", b"tryLock", {"retval": {"type": "Z"}})
    r(b"NSConditionLock", b"tryLockWhenCondition:", {"retval": {"type": "Z"}})
    r(b"NSConnection", b"independentConversationQueueing", {"retval": {"type": "Z"}})
    r(b"NSConnection", b"isValid", {"retval": {"type": "Z"}})
    r(b"NSConnection", b"multipleThreadsEnabled", {"retval": {"type": "Z"}})
    r(b"NSConnection", b"registerName:", {"retval": {"type": "Z"}})
    r(b"NSConnection", b"registerName:withNameServer:", {"retval": {"type": "Z"}})
    r(
        b"NSConnection",
        b"setIndependentConversationQueueing:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"NSData", b"bytes", {"retval": {"c_array_of_variable_length": True}})
    r(
        b"NSData",
        b"compressedDataUsingAlgorithm:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSData",
        b"dataWithBytes:length:",
        {
            "arguments": {
                2: {"type": "^v", "type_modifier": b"n", "c_array_length_in_arg": 3}
            }
        },
    )
    r(
        b"NSData",
        b"dataWithBytesNoCopy:length:",
        {
            "arguments": {
                2: {"type": "^v", "type_modifier": b"n", "c_array_length_in_arg": 3}
            }
        },
    )
    r(
        b"NSData",
        b"dataWithBytesNoCopy:length:freeWhenDone:",
        {
            "arguments": {
                2: {"type": "^v", "type_modifier": b"n", "c_array_length_in_arg": 3},
                4: {"type": "Z"},
            }
        },
    )
    r(
        b"NSData",
        b"dataWithContentsOfFile:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSData",
        b"dataWithContentsOfURL:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSData",
        b"decompressedDataUsingAlgorithm:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSData",
        b"enumerateByteRangesUsingBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {
                                "type": b"^v",
                                "type_modifier": "n",
                                "c_array_length_in_arg": 2,
                            },
                            2: {"type": b"{_NSRange=QQ}"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSData",
        b"getBytes:",
        {"arguments": {2: {"type": "^v"}}, "suggestion": "use -bytes instead"},
    )
    r(
        b"NSData",
        b"getBytes:length:",
        {"arguments": {2: {"type_modifier": b"o", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSData",
        b"getBytes:range:",
        {
            "arguments": {
                2: {"type_modifier": b"o", "c_array_length_in_arg": 3},
                3: {"type": "{_NSRange=QQ}"},
            }
        },
    )
    r(
        b"NSData",
        b"initWithBytes:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSData",
        b"initWithBytesNoCopy:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSData",
        b"initWithBytesNoCopy:length:deallocator:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {
                                "type": b"^v",
                                "type_modifier": "n",
                                "c_array_length_in_arg": 2,
                            },
                            2: {"type": b"Q"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSData",
        b"initWithBytesNoCopy:length:freeWhenDone:",
        {
            "arguments": {
                2: {"type": "^v", "type_modifier": b"n", "c_array_length_in_arg": 3},
                4: {"type": "Z"},
            }
        },
    )
    r(
        b"NSData",
        b"initWithContentsOfFile:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSData",
        b"initWithContentsOfURL:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"NSData", b"isEqualToData:", {"retval": {"type": "Z"}})
    r(b"NSData", b"rangeOfData:options:range:", {"retval": {"type": "{_NSRange=QQ}"}})
    r(b"NSData", b"subdataWithRange:", {"arguments": {2: {"type": "{_NSRange=QQ}"}}})
    r(
        b"NSData",
        b"writeToFile:atomically:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSData",
        b"writeToFile:options:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSData",
        b"writeToURL:atomically:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSData",
        b"writeToURL:options:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSDataDetector",
        b"dataDetectorWithTypes:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSDataDetector",
        b"initWithTypes:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"NSDate", b"isEqualToDate:", {"retval": {"type": "Z"}})
    r(b"NSDateComponents", b"isLeapMonth", {"retval": {"type": b"Z"}})
    r(b"NSDateComponents", b"isValidDate", {"retval": {"type": b"Z"}})
    r(b"NSDateComponents", b"isValidDateInCalendar:", {"retval": {"type": b"Z"}})
    r(b"NSDateComponents", b"setLeapMonth:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"NSDateComponentsFormatter",
        b"allowsFractionalUnits",
        {"retval": {"type": b"Z"}},
    )
    r(b"NSDateComponentsFormatter", b"collapsesLargestUnit", {"retval": {"type": b"Z"}})
    r(
        b"NSDateComponentsFormatter",
        b"getObjectValue:forString:errorDescription:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSDateComponentsFormatter",
        b"includesApproximationPhrase",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"NSDateComponentsFormatter",
        b"includesTimeRemainingPhrase",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"NSDateComponentsFormatter",
        b"setAllowsFractionalUnits:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSDateComponentsFormatter",
        b"setCollapsesLargestUnit:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSDateComponentsFormatter",
        b"setIncludesApproximationPhrase:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSDateComponentsFormatter",
        b"setIncludesTimeRemainingPhrase:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"NSDateFormatter", b"allowsNaturalLanguage", {"retval": {"type": "Z"}})
    r(b"NSDateFormatter", b"doesRelativeDateFormatting", {"retval": {"type": "Z"}})
    r(b"NSDateFormatter", b"generatesCalendarDates", {"retval": {"type": "Z"}})
    r(
        b"NSDateFormatter",
        b"getObjectValue:forString:range:error:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type_modifier": b"o"},
                4: {"type": "^{_NSRange=QQ}", "type_modifier": b"N"},
                5: {"type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSDateFormatter",
        b"initWithDateFormat:allowNaturalLanguage:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(b"NSDateFormatter", b"isLenient", {"retval": {"type": "Z"}})
    r(
        b"NSDateFormatter",
        b"setDoesRelativeDateFormatting:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSDateFormatter",
        b"setGeneratesCalendarDates:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"NSDateFormatter", b"setLenient:", {"arguments": {2: {"type": "Z"}}})
    r(b"NSDateInterval", b"containsDate:", {"retval": {"type": "Z"}})
    r(b"NSDateInterval", b"intersectsDateInterval:", {"retval": {"type": "Z"}})
    r(b"NSDateInterval", b"isEqualToDateInterval:", {"retval": {"type": "Z"}})
    r(
        b"NSDecimalNumber",
        b"decimalNumberWithDecimal:",
        {"arguments": {2: {"type": "{NSDecimal=b8b4b1b1b18[8S]}"}}},
    )
    r(
        b"NSDecimalNumber",
        b"decimalNumberWithMantissa:exponent:isNegative:",
        {"arguments": {4: {"type": "Z"}}},
    )
    r(
        b"NSDecimalNumber",
        b"decimalValue",
        {"retval": {"type": b"{NSDecimal=b8b4b1b1b18[8S]}"}},
    )
    r(
        b"NSDecimalNumber",
        b"initWithDecimal:",
        {"arguments": {2: {"type": "{NSDecimal=b8b4b1b1b18[8S]}"}}},
    )
    r(
        b"NSDecimalNumber",
        b"initWithMantissa:exponent:isNegative:",
        {"arguments": {4: {"type": "Z"}}},
    )
    r(
        b"NSDecimalNumber",
        b"objCType",
        {"retval": {"c_array_delimited_by_null": True, "type": "^t"}},
    )
    r(
        b"NSDecimalNumberHandler",
        b"decimalNumberHandlerWithRoundingMode:scale:raiseOnExactness:raiseOnOverflow:raiseOnUnderflow:raiseOnDivideByZero:",
        {
            "arguments": {
                4: {"type": "Z"},
                5: {"type": "Z"},
                6: {"type": "Z"},
                7: {"type": "Z"},
            }
        },
    )
    r(
        b"NSDecimalNumberHandler",
        b"initWithRoundingMode:scale:raiseOnExactness:raiseOnOverflow:raiseOnUnderflow:raiseOnDivideByZero:",
        {
            "arguments": {
                4: {"type": "Z"},
                5: {"type": "Z"},
                6: {"type": "Z"},
                7: {"type": "Z"},
            }
        },
    )
    r(
        b"NSDictionary",
        b"countByEnumeratingWithState:objects:count:",
        {"arguments": {2: {"type": b"^{_NSFastEnumerationState=Q^@^Q[5Q]}"}}},
    )
    r(
        b"NSDictionary",
        b"dictionaryWithContentsOfURL:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSDictionary",
        b"dictionaryWithObjects:forKeys:count:",
        {
            "arguments": {
                2: {"type_modifier": b"n", "c_array_length_in_arg": 4},
                3: {"type_modifier": b"n", "c_array_length_in_arg": 4},
            }
        },
    )
    r(
        b"NSDictionary",
        b"dictionaryWithObjectsAndKeys:",
        {"c_array_delimited_by_null": True, "variadic": True},
    )
    r(
        b"NSDictionary",
        b"enumerateKeysAndObjectsUsingBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSDictionary",
        b"enumerateKeysAndObjectsWithOptions:usingBlock:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(b"NSDictionary", b"fileExtensionHidden", {"retval": {"type": "Z"}})
    r(b"NSDictionary", b"fileIsAppendOnly", {"retval": {"type": "Z"}})
    r(b"NSDictionary", b"fileIsImmutable", {"retval": {"type": "Z"}})
    r(
        b"NSDictionary",
        b"getObjects:andKeys:",
        {
            "arguments": {2: {"type": "^@"}, 3: {"type": "^@"}},
            "suggestion": "convert to a python dict instead",
        },
    )
    r(
        b"NSDictionary",
        b"initWithContentsOfURL:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSDictionary",
        b"initWithDictionary:copyItems:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSDictionary",
        b"initWithObjects:forKeys:count:",
        {
            "arguments": {
                2: {"type_modifier": b"n", "c_array_length_in_arg": 4},
                3: {"type_modifier": b"n", "c_array_length_in_arg": 4},
            }
        },
    )
    r(
        b"NSDictionary",
        b"initWithObjectsAndKeys:",
        {"c_array_delimited_by_null": True, "variadic": True},
    )
    r(b"NSDictionary", b"isEqualToDictionary:", {"retval": {"type": "Z"}})
    r(
        b"NSDictionary",
        b"keysOfEntriesPassingTest:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSDictionary",
        b"keysOfEntriesWithOptions:passingTest:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSDictionary",
        b"keysSortedByValueUsingComparator:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSDictionary",
        b"keysSortedByValueUsingSelector:",
        {"arguments": {2: {"sel_of_type": b"i@:@"}}},
    )
    r(
        b"NSDictionary",
        b"keysSortedByValueWithOptions:usingComparator:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"q"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSDictionary",
        b"writeToFile:atomically:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSDictionary",
        b"writeToURL:atomically:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSDictionary",
        b"writeToURL:error:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSDirectoryEnumerator",
        b"isEnumeratingDirectoryPostOrder",
        {"retval": {"type": b"Z"}},
    )
    r(b"NSDistributedLock", b"tryLock", {"retval": {"type": "Z"}})
    r(
        b"NSDistributedNotificationCenter",
        b"addObserver:selector:name:object:",
        {"arguments": {3: {"sel_of_type": b"v@:@"}}},
    )
    r(
        b"NSDistributedNotificationCenter",
        b"addObserver:selector:name:object:suspensionBehavior:",
        {"arguments": {3: {"sel_of_type": b"v@:@"}}},
    )
    r(
        b"NSDistributedNotificationCenter",
        b"postNotificationName:object:userInfo:deliverImmediately:",
        {"arguments": {5: {"type": "Z"}}},
    )
    r(
        b"NSDistributedNotificationCenter",
        b"setSuspended:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"NSDistributedNotificationCenter", b"suspended", {"retval": {"type": "Z"}})
    r(
        b"NSEnergyFormatter",
        b"getObjectValue:forString:errorDescription:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"NSEnergyFormatter", b"isForFoodEnergyUse", {"retval": {"type": b"Z"}})
    r(b"NSEnergyFormatter", b"setForFoodEnergyUse:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"NSError",
        b"setUserInfoValueProviderForDomain:provider:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"@"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSError",
        b"userInfoValueProviderForDomain:",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"@"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"NSException",
        b"raise:format:",
        {"arguments": {3: {"printf_format": True, "type": "@"}}, "variadic": True},
    )
    r(
        b"NSException",
        b"raise:format:arguments:",
        {
            "arguments": {4: {"type": "[1{?=II^v^v}]"}},
            "suggestion": "use raise:format:",
        },
    )
    r(
        b"NSExpression",
        b"expressionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"@"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                        3: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(
        b"NSExpression",
        b"expressionForBlock:arguments:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"@"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSExpression",
        b"expressionWithFormat:",
        {"arguments": {2: {"printf_format": True}}, "variadic": True},
    )
    r(
        b"NSExtensionContext",
        b"completeRequestReturningItems:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"NSExtensionContext",
        b"openURL:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileCoordinator",
        b"coordinateAccessWithIntents:queue:byAccessor:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileCoordinator",
        b"coordinateReadingItemAtURL:options:error:byAccessor:",
        {
            "arguments": {
                4: {"type_modifier": b"o"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                },
            }
        },
    )
    r(
        b"NSFileCoordinator",
        b"coordinateReadingItemAtURL:options:writingItemAtURL:options:error:byAccessor:",
        {
            "arguments": {
                6: {"type_modifier": b"o"},
                7: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"NSFileCoordinator",
        b"coordinateWritingItemAtURL:options:error:byAccessor:",
        {
            "arguments": {
                4: {"type_modifier": b"o"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                },
            }
        },
    )
    r(
        b"NSFileCoordinator",
        b"coordinateWritingItemAtURL:options:writingItemAtURL:options:error:byAccessor:",
        {
            "arguments": {
                6: {"type_modifier": b"o"},
                7: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"NSFileCoordinator",
        b"prepareForReadingItemsAtURLs:options:writingItemsAtURLs:options:error:byAccessor:",
        {
            "arguments": {
                6: {"type_modifier": b"o"},
                7: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {
                                "callable": {
                                    "retval": {"type": "v"},
                                    "arguments": {0: {"type": "^v"}},
                                },
                                "type": b"@?",
                            },
                        },
                    }
                },
            }
        },
    )
    r(
        b"NSFileHandle",
        b"closeAndReturnError:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileHandle",
        b"fileHandleForReadingFromURL:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileHandle",
        b"fileHandleForUpdatingURL:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileHandle",
        b"fileHandleForWritingToURL:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileHandle",
        b"getOffset:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileHandle",
        b"initWithFileDescriptor:closeOnDealloc:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSFileHandle",
        b"readDataToEndOfFileAndReturnError:",
        {"arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileHandle",
        b"readDataUpToLength:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileHandle",
        b"readabilityHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"NSFileHandle",
        b"seekToEndReturningOffset:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileHandle",
        b"seekToOffset:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileHandle",
        b"setReadabilityHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileHandle",
        b"setWriteabilityHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileHandle",
        b"synchronizeAndReturnError:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileHandle",
        b"truncateAtOffset:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileHandle",
        b"writeData:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileHandle",
        b"writeabilityHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"NSFileManager",
        b"URLForDirectory:inDomain:appropriateForURL:create:error:",
        {"arguments": {5: {"type": "Z"}, 6: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileManager",
        b"URLForPublishingUbiquitousItemAtURL:expirationDate:error:",
        {"arguments": {3: {"type_modifier": b"o"}, 4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileManager",
        b"attributesOfFileSystemForPath:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileManager",
        b"attributesOfItemAtPath:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"NSFileManager", b"changeCurrentDirectoryPath:", {"retval": {"type": "Z"}})
    r(b"NSFileManager", b"changeFileAttributes:atPath:", {"retval": {"type": "Z"}})
    r(b"NSFileManager", b"contentsEqualAtPath:andPath:", {"retval": {"type": "Z"}})
    r(
        b"NSFileManager",
        b"contentsOfDirectoryAtPath:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileManager",
        b"contentsOfDirectoryAtURL:includingPropertiesForKeys:options:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileManager",
        b"copyItemAtPath:toPath:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileManager",
        b"copyItemAtURL:toURL:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"NSFileManager", b"copyPath:toPath:handler:", {"retval": {"type": "Z"}})
    r(b"NSFileManager", b"createDirectoryAtPath:attributes:", {"retval": {"type": "Z"}})
    r(
        b"NSFileManager",
        b"createDirectoryAtPath:withIntermediateDirectories:attributes:error:",
        {
            "retval": {"type": "Z"},
            "arguments": {3: {"type": "Z"}, 5: {"type_modifier": b"o"}},
        },
    )
    r(
        b"NSFileManager",
        b"createDirectoryAtURL:withIntermediateDirectories:attributes:error:",
        {
            "retval": {"type": "Z"},
            "arguments": {3: {"type": "Z"}, 5: {"type_modifier": b"o"}},
        },
    )
    r(
        b"NSFileManager",
        b"createFileAtPath:contents:attributes:",
        {"retval": {"type": "Z"}},
    )
    r(
        b"NSFileManager",
        b"createSymbolicLinkAtPath:pathContent:",
        {"retval": {"type": "Z"}},
    )
    r(
        b"NSFileManager",
        b"createSymbolicLinkAtPath:withDestinationPath:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileManager",
        b"createSymbolicLinkAtURL:withDestinationURL:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileManager",
        b"destinationOfSymbolicLinkAtPath:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileManager",
        b"enumeratorAtURL:includingPropertiesForKeys:options:errorHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileManager",
        b"evictUbiquitousItemAtURL:error:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileManager",
        b"fileAttributesAtPath:traverseLink:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(b"NSFileManager", b"fileExistsAtPath:", {"retval": {"type": "Z"}})
    r(
        b"NSFileManager",
        b"fileExistsAtPath:isDirectory:",
        {
            "retval": {"type": "Z"},
            "arguments": {3: {"type": "^Z", "type_modifier": b"o"}},
        },
    )
    r(
        b"NSFileManager",
        b"fileSystemRepresentationWithPath:",
        {"retval": {"c_array_delimited_by_null": True, "type": "^t"}},
    )
    r(
        b"NSFileManager",
        b"getFileProviderMessageInterfacesForItemAtURL:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileManager",
        b"getFileProviderServicesForItemAtURL:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileManager",
        b"getRelationship:ofDirectory:inDomain:toItemAtURL:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {2: {"type_modifier": b"o"}, 6: {"type_modifier": b"o"}},
        },
    )
    r(
        b"NSFileManager",
        b"getRelationship:ofDirectoryAtURL:toItemAtURL:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {2: {"type_modifier": b"o"}, 5: {"type_modifier": b"o"}},
        },
    )
    r(b"NSFileManager", b"isDeletableFileAtPath:", {"retval": {"type": "Z"}})
    r(b"NSFileManager", b"isExecutableFileAtPath:", {"retval": {"type": "Z"}})
    r(b"NSFileManager", b"isReadableFileAtPath:", {"retval": {"type": "Z"}})
    r(b"NSFileManager", b"isUbiquitousItemAtURL:", {"retval": {"type": "Z"}})
    r(b"NSFileManager", b"isWritableFileAtPath:", {"retval": {"type": "Z"}})
    r(
        b"NSFileManager",
        b"linkItemAtPath:toPath:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileManager",
        b"linkItemAtURL:toURL:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"NSFileManager", b"linkPath:toPath:handler:", {"retval": {"type": "Z"}})
    r(
        b"NSFileManager",
        b"moveItemAtPath:toPath:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileManager",
        b"moveItemAtURL:toURL:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"NSFileManager", b"movePath:toPath:handler:", {"retval": {"type": "Z"}})
    r(b"NSFileManager", b"removeFileAtPath:handler:", {"retval": {"type": "Z"}})
    r(
        b"NSFileManager",
        b"removeItemAtPath:error:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileManager",
        b"removeItemAtURL:error:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileManager",
        b"replaceItemAtURL:withItemAtURL:backupItemName:options:resultingItemURL:error:",
        {
            "retval": {"type": "Z"},
            "arguments": {6: {"type_modifier": b"o"}, 7: {"type_modifier": b"o"}},
        },
    )
    r(
        b"NSFileManager",
        b"setAttributes:ofItemAtPath:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileManager",
        b"setUbiquitous:itemAtURL:destinationURL:error:",
        {
            "retval": {"type": "Z"},
            "arguments": {2: {"type": "Z"}, 5: {"type_modifier": b"o"}},
        },
    )
    r(
        b"NSFileManager",
        b"startDownloadingUbiquitousItemAtURL:error:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileManager",
        b"stringWithFileSystemRepresentation:length:",
        {
            "arguments": {
                2: {"type": "^t", "type_modifier": b"n", "c_array_length_in_arg": 3}
            }
        },
    )
    r(
        b"NSFileManager",
        b"subpathsOfDirectoryAtPath:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileManager",
        b"trashItemAtURL:resultingItemURL:error:",
        {
            "retval": {"type": b"Z"},
            "arguments": {3: {"type_modifier": b"o"}, 4: {"type_modifier": b"o"}},
        },
    )
    r(
        b"NSFileManager",
        b"unmountVolumeAtURL:options:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSFileProviderService",
        b"getFileProviderConnectionWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSFileVersion",
        b"addVersionOfItemAtURL:withContentsOfURL:options:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileVersion",
        b"getNonlocalVersionsOfItemAtURL:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"NSFileVersion", b"hasLocalContents", {"retval": {"type": b"Z"}})
    r(b"NSFileVersion", b"hasThumbnail", {"retval": {"type": b"Z"}})
    r(b"NSFileVersion", b"isConflict", {"retval": {"type": b"Z"}})
    r(b"NSFileVersion", b"isDiscardable", {"retval": {"type": b"Z"}})
    r(b"NSFileVersion", b"isResolved", {"retval": {"type": b"Z"}})
    r(
        b"NSFileVersion",
        b"removeAndReturnError:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileVersion",
        b"removeOtherVersionsOfItemAtURL:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileVersion",
        b"replaceItemAtURL:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"NSFileVersion", b"setDiscardable:", {"arguments": {2: {"type": b"Z"}}})
    r(b"NSFileVersion", b"setResolved:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"NSFileVersions",
        b"addVersionOfItemAtURL:withContentsOfURL:options:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(b"NSFileVersions", b"isConflict", {"retval": {"type": "Z"}})
    r(b"NSFileVersions", b"isDiscardable", {"retval": {"type": "Z"}})
    r(b"NSFileVersions", b"isResolved", {"retval": {"type": "Z"}})
    r(
        b"NSFileVersions",
        b"removeAndReturnError:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileVersions",
        b"removeOtherVersionsOfItemAtURL:error:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSFileVersions",
        b"replaceItemAtURL:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"NSFileVersions", b"setConflict:", {"arguments": {2: {"type": "Z"}}})
    r(b"NSFileVersions", b"setDiscardable:", {"arguments": {2: {"type": "Z"}}})
    r(b"NSFileVersions", b"setResolved:", {"arguments": {2: {"type": "Z"}}})
    r(b"NSFileWrapper", b"isDirectory", {"retval": {"type": b"Z"}})
    r(b"NSFileWrapper", b"isRegularFile", {"retval": {"type": b"Z"}})
    r(b"NSFileWrapper", b"isSymbolicLink", {"retval": {"type": b"Z"}})
    r(b"NSFileWrapper", b"matchesContentsOfURL:", {"retval": {"type": b"Z"}})
    r(b"NSFileWrapper", b"needsToBeUpdatedFromPath:", {"retval": {"type": b"Z"}})
    r(b"NSFileWrapper", b"readFromURL:options:error:", {"retval": {"type": b"Z"}})
    r(b"NSFileWrapper", b"updateFromPath:", {"retval": {"type": b"Z"}})
    r(
        b"NSFileWrapper",
        b"writeToFile:atomically:updateFilenames:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type": b"Z"}, 4: {"type": b"Z"}}},
    )
    r(
        b"NSFileWrapper",
        b"writeToURL:options:originalContentsURL:error:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"NSFormatter",
        b"getObjectValue:forString:errorDescription:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"null_accepted": False, "type_modifier": b"o"},
                4: {"type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSFormatter",
        b"isPartialStringValid:newEditingString:errorDescription:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                3: {"null_accepted": False, "type_modifier": b"N"},
                4: {"type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSFormatter",
        b"isPartialStringValid:proposedSelectedRange:originalString:originalSelectedRange:errorDescription:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"null_accepted": False, "type_modifier": b"N"},
                3: {"null_accepted": False, "type_modifier": b"N"},
                5: {"type": "{_NSRange=QQ}"},
                6: {"type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSGarbageCollector",
        b"disableCollectorForPointer:",
        {"arguments": {2: {"type": "^v"}}, "suggestion": "Not supported right now"},
    )
    r(
        b"NSGarbageCollector",
        b"enableCollectorForPointer:",
        {"arguments": {2: {"type": "^v"}}, "suggestion": "Not supported right now"},
    )
    r(b"NSGarbageCollector", b"isCollecting", {"retval": {"type": "Z"}})
    r(b"NSGarbageCollector", b"isEnabled", {"retval": {"type": "Z"}})
    r(b"NSHTTPCookie", b"isHTTPOnly", {"retval": {"type": "Z"}})
    r(b"NSHTTPCookie", b"isSecure", {"retval": {"type": "Z"}})
    r(b"NSHTTPCookie", b"isSessionOnly", {"retval": {"type": "Z"}})
    r(
        b"NSHTTPCookieStorage",
        b"getCookiesForTask:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(b"NSHashTable", b"containsObject:", {"retval": {"type": "Z"}})
    r(b"NSHashTable", b"intersectsHashTable:", {"retval": {"type": "Z"}})
    r(b"NSHashTable", b"isEqualToHashTable:", {"retval": {"type": "Z"}})
    r(b"NSHashTable", b"isSubsetOfHashTable:", {"retval": {"type": "Z"}})
    r(b"NSHost", b"isEqualToHost:", {"retval": {"type": "Z"}})
    r(b"NSHost", b"isHostCacheEnabled", {"retval": {"type": "Z"}})
    r(b"NSHost", b"setHostCacheEnabled:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"NSIndexPath",
        b"getIndexes:",
        {
            "arguments": {
                2: {
                    "type": "^Q",
                    "type_modifier": b"o",
                    "c_array_of_variable_length": True,
                }
            },
            "suggestion": "Use -getIndexes:range: or -indexAtPosition: instead",
        },
    )
    r(
        b"NSIndexPath",
        b"getIndexes:range:",
        {
            "arguments": {
                2: {"type": "^Q", "type_modifier": b"o", "c_array_length_in_arg": 3}
            }
        },
    )
    r(
        b"NSIndexPath",
        b"indexPathWithIndexes:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSIndexPath",
        b"initWithIndexes:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(b"NSIndexSet", b"containsIndex:", {"retval": {"type": "Z"}})
    r(b"NSIndexSet", b"containsIndexes:", {"retval": {"type": "Z"}})
    r(
        b"NSIndexSet",
        b"containsIndexesInRange:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSIndexSet",
        b"countOfIndexesInRange:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSIndexSet",
        b"enumerateIndexesInRange:options:usingBlock:",
        {
            "arguments": {
                2: {"type": "{_NSRange=QQ}"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"NSIndexSet",
        b"enumerateIndexesUsingBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSIndexSet",
        b"enumerateIndexesWithOptions:usingBlock:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSIndexSet",
        b"enumerateRangesInRange:options:usingBlock:",
        {
            "arguments": {
                2: {"type": "{_NSRange=QQ}"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{_NSRange=QQ}"},
                            2: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"NSIndexSet",
        b"enumerateRangesUsingBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{_NSRange=QQ}"},
                            2: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSIndexSet",
        b"enumerateRangesWithOptions:usingBlock:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"{_NSRange=QQ}"},
                            2: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSIndexSet",
        b"getIndexes:maxCount:inIndexRange:",
        {
            "arguments": {
                2: {
                    "null_accepted": False,
                    "c_array_length_in_arg": 3,
                    "c_array_length_in_result": True,
                    "type_modifier": b"o",
                },
                4: {"null_accepted": False, "type_modifier": b"N"},
            }
        },
    )
    r(
        b"NSIndexSet",
        b"indexInRange:options:passingTest:",
        {
            "arguments": {
                2: {"type": "{_NSRange=QQ}"},
                4: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"NSIndexSet",
        b"indexPassingTest:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSIndexSet",
        b"indexSetWithIndexesInRange:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSIndexSet",
        b"indexWithOptions:passingTest:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSIndexSet",
        b"indexesInRange:options:passingTest:",
        {
            "arguments": {
                2: {"type": "{_NSRange=QQ}"},
                4: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"NSIndexSet",
        b"indexesPassingTest:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSIndexSet",
        b"indexesWithOptions:passingTest:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSIndexSet",
        b"initWithIndexesInRange:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSIndexSet",
        b"intersectsIndexesInRange:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(b"NSIndexSet", b"isEqualToIndexSet:", {"retval": {"type": "Z"}})
    r(b"NSInflectionRule", b"canInflectLanguage:", {"retval": {"type": b"Z"}})
    r(
        b"NSInflectionRule",
        b"canInflectPreferredLocalization",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"NSInputStream",
        b"getBuffer:length:",
        {
            "retval": {"type": b"Z"},
            "arguments": {
                2: {"type": "^*", "type_modifier": b"o", "c_array_length_in_arg": 3},
                3: {"type": "^Q", "type_modifier": b"o"},
            },
            "suggestion": "Not supported at the moment",
        },
    )
    r(b"NSInputStream", b"hasBytesAvailable", {"retval": {"type": "Z"}})
    r(
        b"NSInputStream",
        b"read:maxLength:",
        {
            "arguments": {
                2: {
                    "type": "^v",
                    "c_array_length_in_arg": 3,
                    "c_array_length_in_result": True,
                    "type_modifier": b"o",
                }
            }
        },
    )
    r(b"NSInvocation", b"argumentsRetained", {"retval": {"type": "Z"}})
    r(b"NSInvocation", b"getArgument:atIndex:", {"arguments": {2: {"type": "^v"}}})
    r(b"NSInvocation", b"getReturnValue:", {"arguments": {2: {"type": "^v"}}})
    r(b"NSInvocation", b"setArgument:atIndex:", {"arguments": {2: {"type": "^v"}}})
    r(b"NSInvocation", b"setReturnValue:", {"arguments": {2: {"type": "^v"}}})
    r(b"NSInvocation", b"setSelector:", {"arguments": {2: {"type": ":"}}})
    r(
        b"NSInvocationOperation",
        b"initWithTarget:selector:object:",
        {"arguments": {3: {"sel_of_type": b"v@:@"}}},
    )
    r(b"NSItemProvider", b"canLoadObjectOfClass:", {"retval": {"type": "Z"}})
    r(
        b"NSItemProvider",
        b"hasItemConformingToTypeIdentifier:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"NSItemProvider",
        b"hasRepresentationConformingToTypeIdentifier:fileOptions:",
        {"retval": {"type": "Z"}},
    )
    r(
        b"NSItemProvider",
        b"loadDataRepresentationForTypeIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSItemProvider",
        b"loadFileRepresentationForTypeIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSItemProvider",
        b"loadInPlaceFileRepresentationForTypeIdentifier:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Z"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSItemProvider",
        b"loadItemForTypeIdentifier:options:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSItemProvider",
        b"loadObjectOfClass:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSItemProvider",
        b"loadPreviewImageWithOptions:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSItemProvider",
        b"previewImageHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {
                            "callable": {
                                "retval": {"type": "v"},
                                "arguments": {
                                    0: {"type": "^v"},
                                    1: {"type": "@"},
                                    2: {"type": "@"},
                                },
                            },
                            "type": b"@?",
                        },
                        2: {"type": b"#"},
                        3: {"type": b"@"},
                    },
                },
                "type": "@?",
            }
        },
    )
    r(
        b"NSItemProvider",
        b"registerDataRepresentationForTypeIdentifier:visibility:loadHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"@"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {
                                "callable": {
                                    "retval": {"type": "v"},
                                    "arguments": {
                                        0: {"type": "^v"},
                                        1: {"type": "@"},
                                        2: {"type": "@"},
                                    },
                                },
                                "type": b"@?",
                            },
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSItemProvider",
        b"registerFileRepresentationForTypeIdentifier:fileOptions:visibility:loadHandler:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"@"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {
                                "callable": {
                                    "retval": {"type": "v"},
                                    "arguments": {
                                        0: {"type": "^v"},
                                        1: {"type": "@"},
                                        2: {"type": "Z"},
                                        3: {"type": "@"},
                                    },
                                },
                                "type": b"@?",
                            },
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSItemProvider",
        b"registerItemForTypeIdentifier:loadHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {
                                "callable": {
                                    "retval": {"type": "v"},
                                    "arguments": {
                                        0: {"type": "^v"},
                                        1: {"type": "@"},
                                        2: {"type": "@"},
                                    },
                                },
                                "type": b"@?",
                            },
                            2: {"type": b"#"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSItemProvider",
        b"registerObjectOfClass:visibility:loadHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"@"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {
                                "callable": {
                                    "retval": {"type": "v"},
                                    "arguments": {
                                        0: {"type": "^v"},
                                        1: {"type": "@"},
                                        2: {"type": "@"},
                                    },
                                },
                                "type": b"@?",
                            },
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSItemProvider",
        b"setPreviewImageHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {
                                "callable": {
                                    "retval": {"type": "v"},
                                    "arguments": {
                                        0: {"type": "^v"},
                                        1: {"type": "@"},
                                        2: {"type": "@"},
                                    },
                                },
                                "type": b"@?",
                            },
                            2: {"type": b"#"},
                            3: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"NSJSONSerialization",
        b"JSONObjectWithData:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSJSONSerialization",
        b"JSONObjectWithStream:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSJSONSerialization",
        b"dataWithJSONObject:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"NSJSONSerialization", b"isValidJSONObject:", {"retval": {"type": b"Z"}})
    r(
        b"NSJSONSerialization",
        b"writeJSONObject:toStream:options:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(b"NSKeyedArchiver", b"archiveRootObject:toFile:", {"retval": {"type": "Z"}})
    r(
        b"NSKeyedArchiver",
        b"archivedDataWithRootObject:requiringSecureCoding:error:",
        {"arguments": {3: {"type": "Z"}, 4: {"type_modifier": b"o"}}},
    )
    r(b"NSKeyedArchiver", b"encodeBool:forKey:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"NSKeyedArchiver",
        b"encodeBytes:length:forKey:",
        {
            "arguments": {
                2: {"type": "^v", "type_modifier": b"n", "c_array_length_in_arg": 3}
            }
        },
    )
    r(
        b"NSKeyedArchiver",
        b"initRequiringSecureCoding:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"NSKeyedArchiver", b"requiresSecureCoding", {"retval": {"type": b"Z"}})
    r(
        b"NSKeyedArchiver",
        b"setRequiresSecureCoding:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"NSKeyedUnarchiver", b"containsValueForKey:", {"retval": {"type": "Z"}})
    r(b"NSKeyedUnarchiver", b"decodeBoolForKey:", {"retval": {"type": "Z"}})
    r(
        b"NSKeyedUnarchiver",
        b"decodeBytesForKey:returnedLength:",
        {
            "retval": {"type": "^v", "c_array_length_in_arg": 3},
            "arguments": {3: {"type_modifier": b"o"}},
        },
    )
    r(
        b"NSKeyedUnarchiver",
        b"initForReadingFromData:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"NSKeyedUnarchiver", b"requiresSecureCoding", {"retval": {"type": b"Z"}})
    r(
        b"NSKeyedUnarchiver",
        b"setRequiresSecureCoding:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSKeyedUnarchiver",
        b"unarchiveTopLevelObjectWithData:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSKeyedUnarchiver",
        b"unarchivedArrayOfObjectsOfClass:fromData:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSKeyedUnarchiver",
        b"unarchivedArrayOfObjectsOfClasses:fromData:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSKeyedUnarchiver",
        b"unarchivedDictionaryWithKeysOfClass:objectsOfClass:fromData:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"NSKeyedUnarchiver",
        b"unarchivedDictionaryWithKeysOfClasses:objectsOfClasses:fromData:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"NSKeyedUnarchiver",
        b"unarchivedObjectOfClass:fromData:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSKeyedUnarchiver",
        b"unarchivedObjectOfClasses:fromData:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSLengthFormatter",
        b"getObjectValue:forString:errorDescription:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"NSLengthFormatter", b"isForPersonHeightUse", {"retval": {"type": b"Z"}})
    r(
        b"NSLengthFormatter",
        b"setForPersonHeightUse:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSLinguisticTagger",
        b"enumerateTagsForString:range:unit:scheme:options:orthography:usingBlock:",
        {
            "arguments": {
                8: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"{_NSRange=QQ}"},
                            3: {"type": b"o^Z"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSLinguisticTagger",
        b"enumerateTagsInRange:scheme:options:usingBlock:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"{_NSRange=QQ}"},
                            3: {"type": b"{_NSRange=QQ}"},
                            4: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSLinguisticTagger",
        b"enumerateTagsInRange:unit:scheme:options:usingBlock:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"{_NSRange=QQ}"},
                            3: {"type": b"o^Z"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSLinguisticTagger",
        b"orthographyAtIndex:effectiveRange:",
        {"arguments": {3: {"type": "^{_NSRange=QQ}", "type_modifier": b"o"}}},
    )
    r(
        b"NSLinguisticTagger",
        b"possibleTagsAtIndex:scheme:tokenRange:sentenceRange:scores:",
        {
            "arguments": {
                4: {"type": "^{_NSRange=QQ}", "type_modifier": b"o"},
                5: {"type": "^{_NSRange=QQ}", "type_modifier": b"o"},
                6: {"type_modifier": b"o"},
            }
        },
    )
    r(
        b"NSLinguisticTagger",
        b"tagAtIndex:scheme:tokenRange:sentenceRange:",
        {
            "arguments": {
                4: {"type": "^{_NSRange=QQ}", "type_modifier": b"o"},
                5: {"type": "^{_NSRange=QQ}", "type_modifier": b"o"},
            }
        },
    )
    r(
        b"NSLinguisticTagger",
        b"tagAtIndex:unit:scheme:tokenRange:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"NSLinguisticTagger",
        b"tagForString:atIndex:unit:scheme:orthography:tokenRange:",
        {"arguments": {7: {"type_modifier": b"o"}}},
    )
    r(
        b"NSLinguisticTagger",
        b"tagsForString:range:unit:scheme:options:orthography:tokenRanges:",
        {"arguments": {8: {"type_modifier": b"o"}}},
    )
    r(
        b"NSLinguisticTagger",
        b"tagsInRange:scheme:options:tokenRanges:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"NSLinguisticTagger",
        b"tagsInRange:unit:scheme:options:tokenRanges:",
        {"arguments": {6: {"type_modifier": b"o"}}},
    )
    r(b"NSLocale", b"usesMetricSystem", {"retval": {"type": b"Z"}})
    r(b"NSLock", b"lockBeforeDate:", {"retval": {"type": "Z"}})
    r(b"NSLock", b"tryLock", {"retval": {"type": "Z"}})
    r(b"NSMachBootstrapServer", b"registerPort:name:", {"retval": {"type": "Z"}})
    r(
        b"NSMassFormatter",
        b"getObjectValue:forString:errorDescription:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"NSMassFormatter", b"isForPersonMassUse", {"retval": {"type": b"Z"}})
    r(b"NSMassFormatter", b"setForPersonMassUse:", {"arguments": {2: {"type": b"Z"}}})
    r(b"NSMeasurement", b"canBeConvertedToUnit:", {"retval": {"type": "Z"}})
    r(
        b"NSMetadataQuery",
        b"enumerateResultsUsingBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"o^Z"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSMetadataQuery",
        b"enumerateResultsWithOptions:usingBlock:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"o^Z"},
                        },
                    }
                }
            }
        },
    )
    r(b"NSMetadataQuery", b"isGathering", {"retval": {"type": "Z"}})
    r(b"NSMetadataQuery", b"isStarted", {"retval": {"type": "Z"}})
    r(b"NSMetadataQuery", b"isStopped", {"retval": {"type": "Z"}})
    r(b"NSMetadataQuery", b"startQuery", {"retval": {"type": "Z"}})
    r(
        b"NSMethodSignature",
        b"getArgumentTypeAtIndex:",
        {"retval": {"c_array_delimited_by_null": True, "type": "^t"}},
    )
    r(b"NSMethodSignature", b"isOneway", {"retval": {"type": "Z"}})
    r(
        b"NSMethodSignature",
        b"methodReturnType",
        {"retval": {"c_array_delimited_by_null": True, "type": "^t"}},
    )
    r(
        b"NSMethodSignature",
        b"signatureWithObjCTypes:",
        {
            "arguments": {
                2: {
                    "c_array_delimited_by_null": True,
                    "type": "^t",
                    "type_modifier": b"n",
                }
            }
        },
    )
    r(b"NSMorphology", b"isUnspecified", {"retval": {"type": b"Z"}})
    r(
        b"NSMorphology",
        b"setCustomPronoun:forLanguage:error:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSMorphologyCustomPronoun",
        b"isSupportedForLanguage:",
        {"retval": {"type": b"Z"}},
    )
    r(b"NSMutableArray", b"context:", {"arguments": {2: {"type": "^v"}}})
    r(
        b"NSMutableArray",
        b"removeObject:inRange:",
        {"arguments": {3: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSMutableArray",
        b"removeObjectIdenticalTo:inRange:",
        {"arguments": {3: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSMutableArray",
        b"removeObjectsFromIndices:numIndices:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSMutableArray",
        b"removeObjectsInRange:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSMutableArray",
        b"replaceObjectsInRange:withObjects:count:",
        {"arguments": {3: {"type_modifier": b"n", "c_array_length_in_arg": 4}}},
    )
    r(
        b"NSMutableArray",
        b"replaceObjectsInRange:withObjectsFromArray:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSMutableArray",
        b"replaceObjectsInRange:withObjectsFromArray:range:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}, 4: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSMutableArray",
        b"sortUsingComparator:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"q"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSMutableArray",
        b"sortUsingFunction:context:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"l"},
                        "arguments": {
                            0: {"type": b"@"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "callable_retained": False,
                },
                3: {"type": "@"},
            }
        },
    )
    r(
        b"NSMutableArray",
        b"sortUsingFunction:context:range:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"l"},
                        "arguments": {
                            0: {"type": b"@"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "callable_retained": False,
                },
                3: {"type": "@"},
                4: {"type": "{_NSRange=QQ}"},
            }
        },
    )
    r(
        b"NSMutableArray",
        b"sortUsingSelector:",
        {"arguments": {2: {"sel_of_type": b"i@:@"}}},
    )
    r(
        b"NSMutableArray",
        b"sortWithOptions:usingComparator:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"q"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSMutableAttributedString",
        b"addAttribute:value:range:",
        {"arguments": {4: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSMutableAttributedString",
        b"addAttributes:range:",
        {"arguments": {3: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSMutableAttributedString",
        b"appendLocalizedFormat:",
        {"arguments": {2: {"printf_format": True}}, "variadic": True},
    )
    r(
        b"NSMutableAttributedString",
        b"deleteCharactersInRange:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSMutableAttributedString",
        b"removeAttribute:range:",
        {"arguments": {3: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSMutableAttributedString",
        b"replaceCharactersInRange:withAttributedString:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSMutableAttributedString",
        b"replaceCharactersInRange:withString:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSMutableAttributedString",
        b"setAttributes:range:",
        {"arguments": {3: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSMutableCharacterSet",
        b"addCharactersInRange:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSMutableCharacterSet",
        b"removeCharactersInRange:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSMutableData",
        b"appendBytes:length:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSMutableData",
        b"compressUsingAlgorithm:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSMutableData",
        b"decompressUsingAlgorithm:error:",
        {"retval": {"type": b"Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSMutableData",
        b"mutableBytes",
        {
            "retval": {"type": "^v"},
            "suggestion": "use your language native array access on this object",
        },
    )
    r(
        b"NSMutableData",
        b"replaceBytesInRange:withBytes:",
        {
            "arguments": {
                2: {"type": "{_NSRange=QQ}"},
                3: {"type_modifier": b"n", "c_array_length_in_arg": 2},
            }
        },
    )
    r(
        b"NSMutableData",
        b"replaceBytesInRange:withBytes:length:",
        {
            "arguments": {
                2: {"type": "{_NSRange=QQ}"},
                3: {"type_modifier": b"n", "c_array_length_in_arg": 4},
            }
        },
    )
    r(
        b"NSMutableData",
        b"resetBytesInRange:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSMutableIndexSet",
        b"addIndexesInRange:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSMutableIndexSet",
        b"removeIndexesInRange:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSMutableOrderedSet",
        b"addObjects:count:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSMutableOrderedSet",
        b"replaceObjectsInRange:withObjects:count:",
        {"arguments": {3: {"type_modifier": b"n", "c_array_length_in_arg": 4}}},
    )
    r(
        b"NSMutableOrderedSet",
        b"sortRange:options:usingComparator:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"q"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSMutableOrderedSet",
        b"sortUsingComparator:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"q"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSMutableOrderedSet",
        b"sortWithOptions:usingComparator:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"q"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSMutableString",
        b"appendFormat:",
        {"arguments": {2: {"printf_format": True, "type": "@"}}, "variadic": True},
    )
    r(
        b"NSMutableString",
        b"applyTransform:reverse:range:updatedRange:",
        {
            "retval": {"type": b"Z"},
            "arguments": {3: {"type": "Z"}, 5: {"type_modifier": b"o"}},
        },
    )
    r(
        b"NSMutableString",
        b"deleteCharactersInRange:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSMutableString",
        b"replaceCharactersInRange:withString:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSMutableString",
        b"replaceOccurrencesOfString:withString:options:range:",
        {"arguments": {5: {"type": "{_NSRange=QQ}"}}},
    )
    r(b"NSMutableURLRequest", b"HTTPShouldHandleCookies", {"retval": {"type": b"Z"}})
    r(b"NSMutableURLRequest", b"HTTPShouldUsePipelining", {"retval": {"type": b"Z"}})
    r(b"NSMutableURLRequest", b"allowsCellularAccess", {"retval": {"type": b"Z"}})
    r(
        b"NSMutableURLRequest",
        b"allowsConstrainedNetworkAccess",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"NSMutableURLRequest",
        b"allowsExpensiveNetworkAccess",
        {"retval": {"type": b"Z"}},
    )
    r(b"NSMutableURLRequest", b"allowsPersistentDNS", {"retval": {"type": b"Z"}})
    r(b"NSMutableURLRequest", b"assumesHTTP3Capable", {"retval": {"type": b"Z"}})
    r(b"NSMutableURLRequest", b"requiresDNSSECValidation", {"retval": {"type": b"Z"}})
    r(
        b"NSMutableURLRequest",
        b"setAllowsCellularAccess:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSMutableURLRequest",
        b"setAllowsConstrainedNetworkAccess:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSMutableURLRequest",
        b"setAllowsExpensiveNetworkAccess:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSMutableURLRequest",
        b"setAllowsPersistentDNS:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSMutableURLRequest",
        b"setAssumesHTTP3Capable:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSMutableURLRequest",
        b"setHTTPShouldHandleCookies:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSMutableURLRequest",
        b"setHTTPShouldUsePipelining:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSMutableURLRequest",
        b"setRequiresDNSSECValidation:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSNetService",
        b"getInputStream:outputStream:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"null_accepted": False, "type_modifier": b"o"},
                3: {"null_accepted": False, "type_modifier": b"o"},
            },
        },
    )
    r(b"NSNetService", b"includesPeerToPeer", {"retval": {"type": "Z"}})
    r(
        b"NSNetService",
        b"setIncludesPeerToPeer:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(b"NSNetService", b"setTXTRecordData:", {"retval": {"type": "Z"}})
    r(b"NSNetServiceBrowser", b"includesPeerToPeer", {"retval": {"type": "Z"}})
    r(
        b"NSNetServiceBrowser",
        b"setIncludesPeerToPeer:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSNotificationCenter",
        b"addObserver:selector:name:object:",
        {"arguments": {3: {"sel_of_type": b"v@:@"}}},
    )
    r(
        b"NSNotificationCenter",
        b"addObserverForName:object:queue:usingBlock:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSNotificationCenter",
        b"addObserverForName:object:usingBlock:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(b"NSNumber", b"boolValue", {"retval": {"type": "Z"}})
    r(b"NSNumber", b"charValue", {"retval": {"type": "z"}})
    r(b"NSNumber", b"decimalValue", {"retval": {"type": "{NSDecimal=b8b4b1b1b18[8S]}"}})
    r(b"NSNumber", b"initWithBool:", {"arguments": {2: {"type": "Z"}}})
    r(b"NSNumber", b"initWithChar:", {"arguments": {2: {"type": "z"}}})
    r(b"NSNumber", b"isEqualToNumber:", {"retval": {"type": "Z"}})
    r(b"NSNumber", b"numberWithBool:", {"arguments": {2: {"type": "Z"}}})
    r(b"NSNumber", b"numberWithChar:", {"arguments": {2: {"type": "z"}}})
    r(b"NSNumberFormatter", b"allowsFloats", {"retval": {"type": "Z"}})
    r(b"NSNumberFormatter", b"alwaysShowsDecimalSeparator", {"retval": {"type": "Z"}})
    r(b"NSNumberFormatter", b"generatesDecimalNumbers", {"retval": {"type": "Z"}})
    r(
        b"NSNumberFormatter",
        b"getObjectValue:forString:range:error:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type_modifier": b"o"},
                4: {"type_modifier": b"N"},
                5: {"type_modifier": b"o"},
            },
        },
    )
    r(b"NSNumberFormatter", b"hasThousandSeparators", {"retval": {"type": "Z"}})
    r(b"NSNumberFormatter", b"isLenient", {"retval": {"type": "Z"}})
    r(
        b"NSNumberFormatter",
        b"isPartialStringValidationEnabled",
        {"retval": {"type": "Z"}},
    )
    r(b"NSNumberFormatter", b"localizesFormat", {"retval": {"type": "Z"}})
    r(b"NSNumberFormatter", b"setAllowsFloats:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"NSNumberFormatter",
        b"setAlwaysShowsDecimalSeparator:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSNumberFormatter",
        b"setGeneratesDecimalNumbers:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSNumberFormatter",
        b"setHasThousandSeparators:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"NSNumberFormatter", b"setLenient:", {"arguments": {2: {"type": "Z"}}})
    r(b"NSNumberFormatter", b"setLocalizesFormat:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"NSNumberFormatter",
        b"setPartialStringValidationEnabled:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSNumberFormatter",
        b"setUsesGroupingSeparator:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSNumberFormatter",
        b"setUsesSignificantDigits:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": "Z"}}},
    )
    r(b"NSNumberFormatter", b"usesGroupingSeparator", {"retval": {"type": "Z"}})
    r(b"NSNumberFormatter", b"usesSignificantDigits", {"retval": {"type": "Z"}})
    r(
        b"NSObject",
        b"URL:resourceDataDidBecomeAvailable:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"URL:resourceDidFailLoadingWithReason:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"URLHandle:resourceDataDidBecomeAvailable:",
        {
            "required": True,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLHandle:resourceDidFailLoadingWithReason:",
        {
            "required": True,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLHandleResourceDidBeginLoading:",
        {"required": True, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"URLHandleResourceDidCancelLoading:",
        {"required": True, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"URLHandleResourceDidFinishLoading:",
        {"required": True, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"URLProtocol:cachedResponseIsValid:",
        {
            "required": True,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLProtocol:didCancelAuthenticationChallenge:",
        {
            "required": True,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLProtocol:didFailWithError:",
        {
            "required": True,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLProtocol:didLoadData:",
        {
            "required": True,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLProtocol:didReceiveAuthenticationChallenge:",
        {
            "required": True,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLProtocol:didReceiveResponse:cacheStoragePolicy:",
        {
            "required": True,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": "Q"}},
        },
    )
    r(
        b"NSObject",
        b"URLProtocol:wasRedirectedToRequest:redirectResponse:",
        {
            "required": True,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLProtocolDidFinishLoading:",
        {"required": True, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"URLResourceDidCancelLoading:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"URLResourceDidFinishLoading:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"URLSession:betterRouteDiscoveredForStreamTask:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLSession:dataTask:didBecomeDownloadTask:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLSession:dataTask:didBecomeStreamTask:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLSession:dataTask:didReceiveData:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLSession:dataTask:didReceiveResponse:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Q"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"URLSession:dataTask:willCacheResponse:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"URLSession:didBecomeInvalidWithError:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLSession:didCreateTask:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLSession:didReceiveChallenge:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"URLSession:downloadTask:didFinishDownloadingToURL:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLSession:downloadTask:didResumeAtOffset:expectedTotalBytes:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"q"},
                5: {"type": b"q"},
            },
        },
    )
    r(
        b"NSObject",
        b"URLSession:downloadTask:didWriteData:totalBytesWritten:totalBytesExpectedToWrite:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"q"},
                5: {"type": b"q"},
                6: {"type": b"q"},
            },
        },
    )
    r(
        b"NSObject",
        b"URLSession:needNewBodyStreamFromOffset:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"NSObject",
        b"URLSession:readClosedForStreamTask:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLSession:streamTask:didBecomeInputStream:outputStream:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"URLSession:task:didCompleteWithError:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLSession:task:didFinishCollectingMetrics:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLSession:task:didReceiveChallenge:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"Q"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"URLSession:task:didReceiveInformationalResponse:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLSession:task:didSendBodyData:totalBytesSent:totalBytesExpectedToSend:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"q"},
                5: {"type": b"q"},
                6: {"type": b"q"},
            },
        },
    )
    r(
        b"NSObject",
        b"URLSession:task:needNewBodyStream:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"URLSession:task:needNewBodyStreamFromOffset:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"q"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"URLSession:task:willBeginDelayedRequest:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"q"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"URLSession:task:willPerformHTTPRedirection:newRequest:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"URLSession:taskIsWaitingForConnectivity:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLSession:webSocketTask:didCloseWithCode:reason:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"q"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"URLSession:webSocketTask:didOpenWithProtocol:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLSession:writeClosedForStreamTask:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"URLSessionDidFinishEventsForBackgroundURLSession:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"accessInstanceVariablesDirectly", {"retval": {"type": b"Z"}})
    r(
        b"NSObject",
        b"accommodatePresentedItemDeletionWithCompletionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"accommodatePresentedItemEvictionWithCompletionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"accommodatePresentedSubitemDeletionAtURL:completionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(
        b"NSObject",
        b"addObserver:forKeyPath:options:context:",
        {
            "retval": {"type": "v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": "I"},
                5: {"type": "^v"},
            },
        },
    )
    r(b"NSObject", b"allowsWeakReference", {"retval": {"type": "Z"}})
    r(
        b"NSObject",
        b"archiver:didEncodeObject:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"archiver:willEncodeObject:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"archiver:willReplaceObject:withObject:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"archiverDidFinish:",
        {"required": False, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"archiverWillFinish:",
        {"required": False, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"attemptRecoveryFromError:optionIndex:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}, 3: {"type": "Q"}}},
    )
    r(
        b"NSObject",
        b"attemptRecoveryFromError:optionIndex:delegate:didRecoverSelector:contextInfo:",
        {
            "retval": {"type": "v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": "Q"},
                4: {"type": b"@"},
                5: {"type": b":", "sel_of_type": b"v@:Z^v"},
                6: {"type": "^v"},
            },
        },
    )
    r(b"NSObject", b"attributeKeys", {"retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"authenticateComponents:withData:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"authenticationDataForComponents:",
        {"required": False, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"autoContentAccessingProxy", {"retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"automaticallyNotifiesObserversForKey:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"autorelease", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"awakeAfterUsingCoder:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"beginContentAccess", {"required": True, "retval": {"type": "Z"}})
    r(
        b"NSObject",
        b"beginRequestWithExtensionContext:",
        {"required": True, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"cache:willEvictObject:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"cancelAuthenticationChallenge:",
        {"required": True, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"cancelPreviousPerformRequestsWithTarget:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"cancelPreviousPerformRequestsWithTarget:selector:object:",
        {
            "retval": {"type": "v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": ":", "sel_of_type": b"v@:@"},
                4: {"type": b"@"},
            },
        },
    )
    r(b"NSObject", b"class", {"required": True, "retval": {"type": b"#"}})
    r(b"NSObject", b"classCode", {"retval": {"type": "Q"}})
    r(b"NSObject", b"classDescription", {"retval": {"type": b"@"}})
    r(b"NSObject", b"classFallbacksForKeyedArchiver", {"retval": {"type": b"@"}})
    r(b"NSObject", b"classForArchiver", {"retval": {"type": "#"}})
    r(b"NSObject", b"classForCoder", {"retval": {"type": "#"}})
    r(b"NSObject", b"classForKeyedArchiver", {"retval": {"type": "#"}})
    r(b"NSObject", b"classForKeyedUnarchiver", {"retval": {"type": "#"}})
    r(b"NSObject", b"classForPortCoder", {"retval": {"type": "#"}})
    r(b"NSObject", b"className", {"retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"coerceValue:forKey:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(b"NSObject", b"commitEditingAndReturnError:", {"arguments": {2: {"type": "o"}}})
    r(
        b"NSObject",
        b"conformsToProtocol:",
        {"required": True, "retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"connection:canAuthenticateAgainstProtectionSpace:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"connection:didCancelAuthenticationChallenge:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"connection:didFailWithError:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"connection:didReceiveAuthenticationChallenge:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"connection:didReceiveData:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"connection:didReceiveResponse:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"connection:didSendBodyData:totalBytesWritten:totalBytesExpectedToWrite:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"q"},
                4: {"type": b"q"},
                5: {"type": b"q"},
            },
        },
    )
    r(
        b"NSObject",
        b"connection:didWriteData:totalBytesWritten:expectedTotalBytes:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"q"},
                4: {"type": b"q"},
                5: {"type": b"q"},
            },
        },
    )
    r(
        b"NSObject",
        b"connection:handleRequest:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"connection:needNewBodyStream:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"connection:shouldMakeNewConnection:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"connection:willCacheResponse:",
        {
            "required": False,
            "retval": {"type": "@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"connection:willSendRequest:redirectResponse:",
        {
            "required": False,
            "retval": {"type": "@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"connection:willSendRequestForAuthenticationChallenge:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"connectionDidFinishDownloading:destinationURL:",
        {
            "required": True,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"connectionDidFinishLoading:",
        {"required": False, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"connectionDidResumeDownloading:totalBytesWritten:expectedTotalBytes:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"q"}, 4: {"type": b"q"}},
        },
    )
    r(
        b"NSObject",
        b"connectionShouldUseCredentialStorage:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"continueWithoutCredentialForAuthenticationChallenge:",
        {"required": True, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"copy", {"retval": {"already_retained": True}})
    r(
        b"NSObject",
        b"copyScriptingValue:forKey:withProperties:",
        {
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"copyWithZone:",
        {
            "required": True,
            "retval": {"already_retained": True, "type": b"@"},
            "arguments": {2: {"type": "^{_NSZone=}"}},
        },
    )
    r(
        b"NSObject",
        b"countByEnumeratingWithState:objects:count:",
        {
            "required": True,
            "retval": {"type": b"Q"},
            "arguments": {
                2: {"type": "^{?=Q^@^Q[5Q]}"},
                3: {"type": "^@"},
                4: {"type": b"Q"},
            },
            "suggestion": "use python iteration",
        },
    )
    r(
        b"NSObject",
        b"createConversationForConnection:",
        {"required": False, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"debugDescription", {"required": False, "retval": {"type": b"@"}})
    r(b"NSObject", b"description", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"dictionaryWithValuesForKeys:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"didChange:valuesAtIndexes:forKey:",
        {
            "retval": {"type": "v"},
            "arguments": {2: {"type": "I"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"didChangeValueForKey:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"didChangeValueForKey:withSetMutation:usingObjects:",
        {
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "I"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"discardContentIfPossible",
        {"required": True, "retval": {"type": b"v"}},
    )
    r(
        b"NSObject",
        b"doesContain:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"doesNotRecognizeSelector:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": ":"}}},
    )
    r(
        b"NSObject",
        b"download:canAuthenticateAgainstProtectionSpace:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"download:decideDestinationWithSuggestedFilename:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"download:didCancelAuthenticationChallenge:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"download:didCreateDestination:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"download:didFailWithError:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"download:didReceiveAuthenticationChallenge:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"download:didReceiveDataOfLength:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "Q"}},
        },
    )
    r(
        b"NSObject",
        b"download:didReceiveResponse:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"download:shouldDecodeSourceDataOfMIMEType:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"download:willResumeWithResponse:fromByte:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": "q"}},
        },
    )
    r(
        b"NSObject",
        b"download:willSendRequest:redirectResponse:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"downloadDidBegin:",
        {"required": False, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"downloadDidFinish:",
        {"required": False, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"downloadShouldUseCredentialStorage:",
        {"required": False, "retval": {"type": b"Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"encodeWithCoder:",
        {"required": True, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"endContentAccess", {"required": True, "retval": {"type": b"v"}})
    r(
        b"NSObject",
        b"exceptionDuringOperation:error:leftOperand:rightOperand:",
        {
            "required": True,
            "retval": {"type": "@"},
            "arguments": {
                2: {"type": ":"},
                3: {"type": "Q"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"fileManager:shouldCopyItemAtPath:toPath:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"fileManager:shouldCopyItemAtURL:toURL:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"fileManager:shouldLinkItemAtPath:toPath:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"fileManager:shouldLinkItemAtURL:toURL:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"fileManager:shouldMoveItemAtPath:toPath:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"fileManager:shouldMoveItemAtURL:toURL:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"fileManager:shouldProceedAfterError:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"fileManager:shouldProceedAfterError:copyingItemAtPath:toPath:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"fileManager:shouldProceedAfterError:copyingItemAtURL:toURL:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"fileManager:shouldProceedAfterError:linkingItemAtPath:toPath:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"fileManager:shouldProceedAfterError:linkingItemAtURL:toURL:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"fileManager:shouldProceedAfterError:movingItemAtPath:toPath:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"fileManager:shouldProceedAfterError:movingItemAtURL:toURL:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"fileManager:shouldProceedAfterError:removingItemAtPath:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"fileManager:shouldProceedAfterError:removingItemAtURL:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"fileManager:shouldRemoveItemAtPath:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"fileManager:shouldRemoveItemAtURL:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"fileManager:willProcessPath:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(b"NSObject", b"forwardInvocation:", {"retval": {"type": "v"}})
    r(
        b"NSObject",
        b"handleMachMessage:",
        {"required": False, "retval": {"type": "v"}, "arguments": {2: {"type": "^v"}}},
    )
    r(
        b"NSObject",
        b"handlePortMessage:",
        {"required": False, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"handleQueryWithUnboundKey:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"handleTakeValue:forUnboundKey:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(b"NSObject", b"hash", {"required": True, "retval": {"type": "Q"}})
    r(
        b"NSObject",
        b"indicesOfObjectsByEvaluatingObjectSpecifier:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"initWithCoder:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"initWithItemProviderData:typeIdentifier:error:",
        {"arguments": {4: {"type": "^@", "type_modifier": b"o"}}},
    )
    r(b"NSObject", b"initialize", {"retval": {"type": "v"}})
    r(
        b"NSObject",
        b"insertValue:atIndex:inPropertyWithKey:",
        {
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "Q"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"insertValue:inPropertyWithKey:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"instanceMethodForSelector:",
        {"retval": {"type": "^?"}, "arguments": {2: {"type": ":"}}},
    )
    r(
        b"NSObject",
        b"instanceMethodSignatureForSelector:",
        {"arguments": {2: {"type": ":"}}},
    )
    r(
        b"NSObject",
        b"instancesRespondToSelector:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": ":"}}},
    )
    r(
        b"NSObject",
        b"inverseForRelationshipKey:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"isCaseInsensitiveLike:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"isContentDiscarded", {"required": True, "retval": {"type": "Z"}})
    r(
        b"NSObject",
        b"isEqual:",
        {"required": True, "retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"isEqualTo:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"isGreaterThan:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"isGreaterThanOrEqualTo:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"isKindOfClass:",
        {"required": True, "retval": {"type": "Z"}, "arguments": {2: {"type": "#"}}},
    )
    r(
        b"NSObject",
        b"isLessThan:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"isLessThanOrEqualTo:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"isLike:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"isMemberOfClass:",
        {"required": True, "retval": {"type": "Z"}, "arguments": {2: {"type": "#"}}},
    )
    r(
        b"NSObject",
        b"isNotEqualTo:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"isProxy", {"required": True, "retval": {"type": "Z"}})
    r(
        b"NSObject",
        b"isSubclassOfClass:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": "#"}}},
    )
    r(
        b"NSObject",
        b"itemProviderVisibilityForRepresentationWithTypeIdentifier:",
        {"required": False, "retval": {"type": "q"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"keyPathsForValuesAffectingValueForKey:",
        {"retval": {"type": "@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"listener:shouldAcceptNewConnection:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(b"NSObject", b"load", {"retval": {"type": "v"}})
    r(
        b"NSObject",
        b"loadDataWithTypeIdentifier:forItemProviderCompletionHandler:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                },
            },
        },
    )
    r(b"NSObject", b"lock", {"required": True, "retval": {"type": "v"}})
    r(
        b"NSObject",
        b"makeNewConnection:sender:",
        {
            "required": False,
            "retval": {"type": "Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"metadataQuery:replacementObjectForResultObject:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"metadataQuery:replacementValueForAttribute:value:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"methodForSelector:",
        {"retval": {"type": "^?"}, "arguments": {2: {"type": ":"}}},
    )
    r(b"NSObject", b"methodSignatureForSelector:", {"arguments": {2: {"type": ":"}}})
    r(
        b"NSObject",
        b"mutableArrayValueForKey:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"mutableArrayValueForKeyPath:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"mutableCopy", {"retval": {"already_retained": True, "type": "@"}})
    r(
        b"NSObject",
        b"mutableCopyWithZone:",
        {
            "required": True,
            "retval": {"already_retained": True, "type": "@"},
            "arguments": {2: {"type": "^{_NSZone=}"}},
        },
    )
    r(
        b"NSObject",
        b"mutableOrderedSetValueForKey:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"mutableOrderedSetValueForKeyPath:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"mutableSetValueForKey:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"mutableSetValueForKeyPath:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"netService:didAcceptConnectionWithInputStream:outputStream:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"netService:didNotPublish:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"netService:didNotResolve:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"netService:didUpdateTXTRecordData:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"netServiceBrowser:didFindDomain:moreComing:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": "Z"}},
        },
    )
    r(
        b"NSObject",
        b"netServiceBrowser:didFindService:moreComing:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": "Z"}},
        },
    )
    r(
        b"NSObject",
        b"netServiceBrowser:didNotSearch:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"netServiceBrowser:didRemoveDomain:moreComing:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": "Z"}},
        },
    )
    r(
        b"NSObject",
        b"netServiceBrowser:didRemoveService:moreComing:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": "Z"}},
        },
    )
    r(
        b"NSObject",
        b"netServiceBrowserDidStopSearch:",
        {"required": False, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"netServiceBrowserWillSearch:",
        {"required": False, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"netServiceDidPublish:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"netServiceDidResolveAddress:",
        {"required": False, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"netServiceDidStop:",
        {"required": False, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"netServiceWillPublish:",
        {"required": False, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"netServiceWillResolve:",
        {"required": False, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"newScriptingObjectOfClass:forValueForKey:withContentsValue:properties:",
        {
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": "#"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(b"NSObject", b"objectSpecifier", {"retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"objectWithItemProviderData:typeIdentifier:error:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": "^@", "type_modifier": b"o"},
            },
        },
    )
    r(b"NSObject", b"observationInfo", {"retval": {"type": "^v"}})
    r(
        b"NSObject",
        b"observeValueForKeyPath:ofObject:change:context:",
        {
            "retval": {"type": "v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": "^v"},
            },
        },
    )
    r(
        b"NSObject",
        b"observedPresentedItemUbiquityAttributes",
        {"required": False, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"parser:didEndElement:namespaceURI:qualifiedName:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"parser:didEndMappingPrefix:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"parser:didStartElement:namespaceURI:qualifiedName:attributes:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"parser:didStartMappingPrefix:toURI:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"parser:foundAttributeDeclarationWithName:forElement:type:defaultValue:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"parser:foundCDATA:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"parser:foundCharacters:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"parser:foundComment:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"parser:foundElementDeclarationWithName:model:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"parser:foundExternalEntityDeclarationWithName:publicID:systemID:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"parser:foundIgnorableWhitespace:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"parser:foundInternalEntityDeclarationWithName:value:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"parser:foundNotationDeclarationWithName:publicID:systemID:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"parser:foundProcessingInstructionWithTarget:data:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"parser:foundUnparsedEntityDeclarationWithName:publicID:systemID:notationName:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"parser:parseErrorOccurred:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"parser:resolveExternalEntityName:systemID:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"parser:validationErrorOccurred:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"parserDidEndDocument:",
        {"required": False, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"parserDidStartDocument:",
        {"required": False, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"performDefaultHandlingForAuthenticationChallenge:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"performSelector:",
        {"required": True, "retval": {"type": b"@"}, "arguments": {2: {"type": ":"}}},
    )
    r(
        b"NSObject",
        b"performSelector:onThread:withObject:waitUntilDone:",
        {
            "retval": {"type": "v"},
            "arguments": {
                2: {"type": b":", "sel_of_type": b"v@:@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": "Z"},
            },
        },
    )
    r(
        b"NSObject",
        b"performSelector:onThread:withObject:waitUntilDone:modes:",
        {
            "retval": {"type": "v"},
            "arguments": {
                2: {"type": b":", "sel_of_type": b"v@:@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": "Z"},
                6: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"performSelector:withObject:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": ":"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"performSelector:withObject:afterDelay:",
        {
            "retval": {"type": "v"},
            "arguments": {
                2: {"type": b":", "sel_of_type": b"v@:@"},
                3: {"type": b"@"},
                4: {"type": "d"},
            },
        },
    )
    r(
        b"NSObject",
        b"performSelector:withObject:afterDelay:inModes:",
        {
            "retval": {"type": "v"},
            "arguments": {
                2: {"type": b":", "sel_of_type": b"v@:@"},
                3: {"type": b"@"},
                4: {"type": "d"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"performSelector:withObject:withObject:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": ":"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"performSelectorInBackground:withObject:",
        {
            "retval": {"type": "v"},
            "arguments": {2: {"type": b":", "sel_of_type": b"v@:@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"performSelectorOnMainThread:withObject:waitUntilDone:",
        {
            "retval": {"type": "v"},
            "arguments": {
                2: {"type": b":", "sel_of_type": b"v@:@"},
                3: {"type": b"@"},
                4: {"type": "Z"},
            },
        },
    )
    r(
        b"NSObject",
        b"performSelectorOnMainThread:withObject:waitUntilDone:modes:",
        {
            "retval": {"type": "v"},
            "arguments": {
                2: {"type": b":", "sel_of_type": b"v@:@"},
                3: {"type": b"@"},
                4: {"type": "Z"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"poseAsClass:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": "#"}}},
    )
    r(
        b"NSObject",
        b"presentedItemDidChange",
        {"required": False, "retval": {"type": b"v"}},
    )
    r(
        b"NSObject",
        b"presentedItemDidChangeUbiquityAttributes:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"presentedItemDidGainVersion:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"presentedItemDidLoseVersion:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"presentedItemDidMoveToURL:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"presentedItemDidResolveConflictVersion:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"presentedItemOperationQueue",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(b"NSObject", b"presentedItemURL", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"presentedSubitemAtURL:didGainVersion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"presentedSubitemAtURL:didLoseVersion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"presentedSubitemAtURL:didMoveToURL:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"presentedSubitemAtURL:didResolveConflictVersion:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"presentedSubitemDidAppearAtURL:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"presentedSubitemDidChangeAtURL:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"primaryPresentedItemURL",
        {"required": False, "retval": {"type": b"@"}},
    )
    r(b"NSObject", b"progress", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"readableTypeIdentifiersForItemProvider",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(
        b"NSObject",
        b"rejectProtectionSpaceAndContinueWithChallenge:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"release", {"required": True, "retval": {"type": "Vv"}})
    r(
        b"NSObject",
        b"relinquishPresentedItemToReader:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {
                                "callable": {
                                    "retval": {"type": "v"},
                                    "arguments": {0: {"type": "^v"}},
                                },
                                "type": b"@?",
                            },
                        },
                    },
                    "type": "@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"relinquishPresentedItemToWriter:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {
                                "callable": {
                                    "retval": {"type": "v"},
                                    "arguments": {0: {"type": "^v"}},
                                },
                                "type": b"@?",
                            },
                        },
                    },
                    "type": "@?",
                }
            },
        },
    )
    r(b"NSObject", b"remoteObjectProxy", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"remoteObjectProxyWithErrorHandler:",
        {
            "required": True,
            "retval": {"type": b"@"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"removeObserver:forKeyPath:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"removeObserver:forKeyPath:context:",
        {
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"^v"}},
        },
    )
    r(
        b"NSObject",
        b"removeValueAtIndex:fromPropertyWithKey:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": "Q"}, 3: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"replaceValueAtIndex:inPropertyWithKey:withValue:",
        {
            "retval": {"type": "v"},
            "arguments": {2: {"type": "Q"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"replacementObjectForArchiver:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"replacementObjectForCoder:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"replacementObjectForKeyedArchiver:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"replacementObjectForPortCoder:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"resolveClassMethod:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": ":"}}},
    )
    r(
        b"NSObject",
        b"resolveInstanceMethod:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": ":"}}},
    )
    r(
        b"NSObject",
        b"respondsToSelector:",
        {"required": True, "retval": {"type": "Z"}, "arguments": {2: {"type": ":"}}},
    )
    r(b"NSObject", b"retain", {"required": True, "retval": {"type": b"@"}})
    r(b"NSObject", b"retainCount", {"required": True, "retval": {"type": "Q"}})
    r(b"NSObject", b"retainWeakReference", {"retval": {"type": "Z"}})
    r(b"NSObject", b"roundingMode", {"required": True, "retval": {"type": "Q"}})
    r(
        b"NSObject",
        b"savePresentedItemChangesWithCompletionHandler:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            },
        },
    )
    r(b"NSObject", b"scale", {"required": True, "retval": {"type": "s"}})
    r(
        b"NSObject",
        b"scriptingBeginsWith:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"scriptingContains:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"scriptingEndsWith:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"scriptingIsEqualTo:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"scriptingIsGreaterThan:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"scriptingIsGreaterThanOrEqualTo:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"scriptingIsLessThan:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"scriptingIsLessThanOrEqualTo:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"scriptingProperties", {"retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"scriptingValueForSpecifier:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"self", {"required": True, "retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"setKeys:triggerChangeNotificationsForDependentKey:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setNilValueForKey:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setObservationInfo:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": "^v"}}},
    )
    r(
        b"NSObject",
        b"setPresentedItemOperationQueue:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setPresentedItemURL:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setPrimaryPresentedItemURL:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setScriptingProperties:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setSharedObservers:",
        {"retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setValue:forKey:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setValue:forKeyPath:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setValue:forUndefinedKey:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setValuesForKeysWithDictionary:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"setVersion:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": "q"}}},
    )
    r(
        b"NSObject",
        b"spellServer:checkGrammarInString:language:details:",
        {
            "required": False,
            "retval": {"type": "{_NSRange=QQ}"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": "^@", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"spellServer:checkString:offset:types:options:orthography:wordCount:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": "Q"},
                5: {"type": "q"},
                6: {"type": b"@"},
                7: {"type": b"@"},
                8: {"type": "^q", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"spellServer:didForgetWord:inLanguage:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"spellServer:didLearnWord:inLanguage:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"spellServer:findMisspelledWordInString:language:wordCount:countOnly:",
        {
            "required": False,
            "retval": {"type": "{_NSRange=QQ}"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"@"},
                4: {"type": b"@"},
                5: {"type": "^q", "type_modifier": b"o"},
                6: {"type": "Z"},
            },
        },
    )
    r(
        b"NSObject",
        b"spellServer:recordResponse:toCorrection:forWord:language:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": b"Q"},
                4: {"type": b"@"},
                5: {"type": b"@"},
                6: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"spellServer:suggestCompletionsForPartialWordRange:inString:language:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {
                2: {"type": b"@"},
                3: {"type": "{_NSRange=QQ}"},
                4: {"type": b"@"},
                5: {"type": b"@"},
            },
        },
    )
    r(
        b"NSObject",
        b"spellServer:suggestGuessesForWord:inLanguage:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"storedValueForKey:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"stream:handleEvent:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "Q"}},
        },
    )
    r(b"NSObject", b"superclass", {"required": True, "retval": {"type": "#"}})
    r(b"NSObject", b"supportsSecureCoding", {"required": True, "retval": {"type": "Z"}})
    r(
        b"NSObject",
        b"synchronousRemoteObjectProxyWithErrorHandler:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": b"@?",
                }
            },
        },
    )
    r(
        b"NSObject",
        b"takeStoredValue:forKey:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"takeValue:forKey:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"takeValue:forKeyPath:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"takeValuesFromDictionary:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"toManyRelationshipKeys", {"retval": {"type": b"@"}})
    r(b"NSObject", b"toOneRelationshipKeys", {"retval": {"type": b"@"}})
    r(
        b"NSObject",
        b"unableToSetNilForKey:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"unarchiver:cannotDecodeObjectOfClassName:originalClasses:",
        {
            "required": False,
            "retval": {"type": "#"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"unarchiver:didDecodeObject:",
        {
            "required": False,
            "retval": {"type": b"@"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"unarchiver:willReplaceObject:withObject:",
        {
            "required": False,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"unarchiverDidFinish:",
        {"required": False, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"unarchiverWillFinish:",
        {"required": False, "retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"unlock", {"required": True, "retval": {"type": "v"}})
    r(
        b"NSObject",
        b"useCredential:forAuthenticationChallenge:",
        {
            "required": True,
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(b"NSObject", b"useStoredAccessor", {"retval": {"type": "Z"}})
    r(
        b"NSObject",
        b"userActivity:didReceiveInputStream:outputStream:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"userActivityWasContinued:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"userActivityWillSave:",
        {"required": False, "retval": {"type": b"v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"userNotificationCenter:didActivateNotification:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"userNotificationCenter:didDeliverNotification:",
        {
            "required": False,
            "retval": {"type": b"v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"userNotificationCenter:shouldPresentNotification:",
        {
            "required": False,
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"validateValue:forKey:error:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": "^@", "type_modifier": b"N"},
                3: {"type": b"@"},
                4: {"type": "^@", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"validateValue:forKeyPath:error:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": "^@", "type_modifier": b"N"},
                3: {"type": "@"},
                4: {"type": "^@", "type_modifier": b"o"},
            },
        },
    )
    r(
        b"NSObject",
        b"valueAtIndex:inPropertyWithKey:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": "Q"}, 3: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"valueForKey:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"valueForKeyPath:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"valueForUndefinedKey:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"valueWithName:inPropertyWithKey:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"valueWithUniqueID:inPropertyWithKey:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}, 3: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"valuesForKeys:",
        {"retval": {"type": b"@"}, "arguments": {2: {"type": b"@"}}},
    )
    r(b"NSObject", b"version", {"retval": {"type": "q"}})
    r(
        b"NSObject",
        b"willChange:valuesAtIndexes:forKey:",
        {
            "retval": {"type": "v"},
            "arguments": {2: {"type": "I"}, 3: {"type": b"@"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"willChangeValueForKey:",
        {"retval": {"type": "v"}, "arguments": {2: {"type": b"@"}}},
    )
    r(
        b"NSObject",
        b"willChangeValueForKey:withSetMutation:usingObjects:",
        {
            "retval": {"type": "v"},
            "arguments": {2: {"type": b"@"}, 3: {"type": "I"}, 4: {"type": b"@"}},
        },
    )
    r(
        b"NSObject",
        b"writableTypeIdentifiersForItemProvider",
        {"required": True, "retval": {"type": b"@"}},
    )
    r(b"NSObject", b"zone", {"required": True, "retval": {"type": b"^{_NSZone=}"}})
    r(
        b"NSOperation",
        b"completionBlock",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}},
                }
            }
        },
    )
    r(b"NSOperation", b"isAsynchronous", {"retval": {"type": b"Z"}})
    r(b"NSOperation", b"isCancelled", {"retval": {"type": "Z"}})
    r(b"NSOperation", b"isConcurrent", {"retval": {"type": "Z"}})
    r(b"NSOperation", b"isExecuting", {"retval": {"type": "Z"}})
    r(b"NSOperation", b"isFinished", {"retval": {"type": "Z"}})
    r(b"NSOperation", b"isReady", {"retval": {"type": "Z"}})
    r(
        b"NSOperation",
        b"setCompletionBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSOperationQueue",
        b"addBarrierBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSOperationQueue",
        b"addOperationWithBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSOperationQueue",
        b"addOperations:waitUntilFinished:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(b"NSOperationQueue", b"isSuspended", {"retval": {"type": "Z"}})
    r(b"NSOperationQueue", b"setSuspended:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"NSOrderedCollectionDifference",
        b"differenceByTransformingChangesWithBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"@"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(b"NSOrderedCollectionDifference", b"hasChanges", {"retval": {"type": b"Z"}})
    r(b"NSOrderedSet", b"containsObject:", {"retval": {"type": "Z"}})
    r(
        b"NSOrderedSet",
        b"differenceFromOrderedSet:withOptions:usingEquivalenceTest:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSOrderedSet",
        b"enumerateObjectsAtIndexes:options:usingBlock:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSOrderedSet",
        b"enumerateObjectsUsingBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSOrderedSet",
        b"enumerateObjectsWithOptions:usingBlock:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSOrderedSet",
        b"indexOfObject:inSortedRange:options:usingComparator:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"q"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSOrderedSet",
        b"indexOfObjectAtIndexes:options:passingTest:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSOrderedSet",
        b"indexOfObjectPassingTest:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSOrderedSet",
        b"indexOfObjectWithOptions:passingTest:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSOrderedSet",
        b"indexesOfObjecstWithOptions:passingTest:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSOrderedSet",
        b"indexesOfObjectsAtIndexes:options:passingTest:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSOrderedSet",
        b"indexesOfObjectsPassingTest:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSOrderedSet",
        b"indexesOfObjectsWithOptions:passingTest:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(b"NSOrderedSet", b"initWithArray:copyItems:", {"arguments": {3: {"type": "Z"}}})
    r(
        b"NSOrderedSet",
        b"initWithArray:range:copyItems:",
        {"arguments": {4: {"type": "Z"}}},
    )
    r(
        b"NSOrderedSet",
        b"initWithObjects:",
        {"c_array_delimited_by_null": True, "variadic": True},
    )
    r(
        b"NSOrderedSet",
        b"initWithObjects:count:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSOrderedSet",
        b"initWithOrderedSet:copyItems:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSOrderedSet",
        b"initWithOrderedSet:range:copyItems:",
        {"arguments": {4: {"type": "Z"}}},
    )
    r(b"NSOrderedSet", b"initWithSet:copyItems:", {"arguments": {3: {"type": "Z"}}})
    r(b"NSOrderedSet", b"insersectsSet:", {"retval": {"type": "Z"}})
    r(b"NSOrderedSet", b"intersectsOrderedSet:", {"retval": {"type": "Z"}})
    r(b"NSOrderedSet", b"intersectsSet:", {"retval": {"type": b"Z"}})
    r(b"NSOrderedSet", b"isEqualToOrderedSet:", {"retval": {"type": "Z"}})
    r(b"NSOrderedSet", b"isSubsetOfOrderedSet:", {"retval": {"type": "Z"}})
    r(b"NSOrderedSet", b"isSubsetOfSet:", {"retval": {"type": "Z"}})
    r(
        b"NSOrderedSet",
        b"orderedSetWithArray:copyItems:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSOrderedSet",
        b"orderedSetWithArray:range:copyItems:",
        {"arguments": {4: {"type": "Z"}}},
    )
    r(
        b"NSOrderedSet",
        b"orderedSetWithObjects:",
        {"c_array_delimited_by_null": True, "variadic": True},
    )
    r(
        b"NSOrderedSet",
        b"orderedSetWithObjects:count:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSOrderedSet",
        b"orderedSetWithOrderedSet:copyItems:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSOrderedSet",
        b"orderedSetWithOrderedSet:range:copyItems:",
        {"arguments": {4: {"type": "Z"}}},
    )
    r(
        b"NSOrderedSet",
        b"orderedSetWithSet:copyItems:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSOrderedSet",
        b"sortedArrayUsingComparator:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"q"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSOrderedSet",
        b"sortedArrayWithOptions:usingComparator:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"q"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"NSOutputStream", b"hasSpaceAvailable", {"retval": {"type": "Z"}})
    r(
        b"NSOutputStream",
        b"initToBuffer:capacity:",
        {
            "arguments": {
                2: {"type": "^v", "type_modifier": b"o", "c_array_length_in_arg": 3}
            }
        },
    )
    r(b"NSOutputStream", b"initToFileAtPath:append:", {"arguments": {3: {"type": "Z"}}})
    r(b"NSOutputStream", b"initWithURL:append:", {"arguments": {3: {"type": "Z"}}})
    r(
        b"NSOutputStream",
        b"outputStreamToBuffer:capacity:",
        {
            "arguments": {
                2: {"type": "^v", "type_modifier": b"o", "c_array_length_in_arg": 3}
            }
        },
    )
    r(
        b"NSOutputStream",
        b"outputStreamToFileAtPath:append:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSOutputStream",
        b"outputStreamWithURL:append:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSOutputStream",
        b"write:maxLength:",
        {
            "arguments": {
                2: {"type": "^v", "type_modifier": b"n", "c_array_length_in_arg": 3}
            }
        },
    )
    r(
        b"NSPersonNameComponentsFormatter",
        b"getObjectValue:forString:errorDescription:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"null_accepted": False, "type_modifier": b"o"},
                4: {"type_modifier": b"o"},
            },
        },
    )
    r(b"NSPersonNameComponentsFormatter", b"isPhonetic", {"retval": {"type": "Z"}})
    r(
        b"NSPersonNameComponentsFormatter",
        b"setPhonetic:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSPointerArray",
        b"addPointer:",
        {"arguments": {2: {"type": "@"}}, "suggestion": "use NSMutableArray"},
    )
    r(
        b"NSPointerArray",
        b"insertPointer:atIndex:",
        {"arguments": {2: {"type": "@"}}, "suggestion": "use NSMutableArray"},
    )
    r(
        b"NSPointerArray",
        b"pointerAtIndex:",
        {"retval": {"type": "@"}, "suggestion": "use NSMutableArray"},
    )
    r(
        b"NSPointerArray",
        b"replacePointerAtIndex:withPointer:",
        {"arguments": {3: {"type": "@"}}, "suggestion": "use NSMutableArray"},
    )
    r(b"NSPointerFunctions", b"acquireFunction", {"retval": {"type": "^v"}})
    r(b"NSPointerFunctions", b"setAcquireFunction:", {"arguments": {2: {"type": "^v"}}})
    r(
        b"NSPointerFunctions",
        b"setUsesStrongWriteBarrier:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSPointerFunctions",
        b"setUsesWeakReadAndWriteBarriers:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"NSPointerFunctions", b"usesStrongWriteBarrier", {"retval": {"type": "Z"}})
    r(b"NSPointerFunctions", b"usesWeakReadAndWriteBarriers", {"retval": {"type": "Z"}})
    r(b"NSPort", b"isValid", {"retval": {"type": "Z"}})
    r(b"NSPort", b"sendBeforeDate:components:from:reserved:", {"retval": {"type": "Z"}})
    r(
        b"NSPort",
        b"sendBeforeDate:msgid:components:from:reserved:",
        {"retval": {"type": "Z"}},
    )
    r(b"NSPortCoder", b"isBycopy", {"retval": {"type": "Z"}})
    r(b"NSPortCoder", b"isByref", {"retval": {"type": "Z"}})
    r(b"NSPortMessage", b"sendBeforeDate:", {"retval": {"type": "Z"}})
    r(b"NSPortNameServer", b"registerPort:name:", {"retval": {"type": "Z"}})
    r(b"NSPortNameServer", b"removePortForName:", {"retval": {"type": "Z"}})
    r(b"NSPositionalSpecifier", b"insertionReplaces", {"retval": {"type": "Z"}})
    r(b"NSPredicate", b"evaluateWithObject:", {"retval": {"type": "Z"}})
    r(
        b"NSPredicate",
        b"evaluateWithObject:substitutionVariables:",
        {"retval": {"type": "Z"}},
    )
    r(
        b"NSPredicate",
        b"predicateWithBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSPredicate",
        b"predicateWithFormat:",
        {"arguments": {2: {"printf_format": True, "type": "@"}}, "variadic": True},
    )
    r(
        b"NSPredicate",
        b"predicateWithFormat:arguments:",
        {"suggestion": "use +predicateWithFormat:"},
    )
    r(b"NSPredicate", b"predicateWithValue:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"NSPresentationIntent",
        b"isEquivalentToPresentationIntent:",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"NSProcessInfo",
        b"automaticTerminationSupportEnabled",
        {"retval": {"type": b"Z"}},
    )
    r(b"NSProcessInfo", b"isLowPowerModeEnabled", {"retval": {"type": b"Z"}})
    r(b"NSProcessInfo", b"isMacCatalystApp", {"retval": {"type": b"Z"}})
    r(
        b"NSProcessInfo",
        b"isOperatingSystemAtLeastVersion:",
        {
            "retval": {"type": b"Z"},
            "arguments": {2: {"type": b"{NSOperatingSystemVersion=qqq}"}},
        },
    )
    r(b"NSProcessInfo", b"isiOSAppOnMac", {"retval": {"type": "Z"}})
    r(
        b"NSProcessInfo",
        b"operatingSystemVersion",
        {"retval": {"type": b"{NSOperatingSystemVersion=qqq}"}},
    )
    r(
        b"NSProcessInfo",
        b"performActivityWithOptions:reason:usingBlock:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSProcessInfo",
        b"performExpiringActivityWithReason:usingBlock:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"Z"}},
                    }
                }
            }
        },
    )
    r(
        b"NSProcessInfo",
        b"setAutomaticTerminationSupportEnabled:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSProgress",
        b"addSubscriberForFileURL:withPublishingHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {
                            "callable": {
                                "retval": {"type": "v"},
                                "arguments": {0: {"type": "^v"}},
                            },
                            "type": b"@?",
                        },
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(
        b"NSProgress",
        b"cancellationHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}},
                }
            }
        },
    )
    r(b"NSProgress", b"isCancellable", {"retval": {"type": b"Z"}})
    r(b"NSProgress", b"isCancelled", {"retval": {"type": b"Z"}})
    r(b"NSProgress", b"isFinished", {"retval": {"type": b"Z"}})
    r(b"NSProgress", b"isIndeterminate", {"retval": {"type": b"Z"}})
    r(b"NSProgress", b"isOld", {"retval": {"type": b"Z"}})
    r(b"NSProgress", b"isPausable", {"retval": {"type": b"Z"}})
    r(b"NSProgress", b"isPaused", {"retval": {"type": b"Z"}})
    r(
        b"NSProgress",
        b"pausingHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}},
                }
            }
        },
    )
    r(
        b"NSProgress",
        b"performAsCurrentWithPendingUnitCount:usingBlock:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSProgress",
        b"resumingHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}},
                }
            }
        },
    )
    r(b"NSProgress", b"setCancellable:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"NSProgress",
        b"setCancellationHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(b"NSProgress", b"setPausable:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"NSProgress",
        b"setPausingHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSProgress",
        b"setResumingHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSPropertyListSerialization",
        b"dataFromPropertyList:format:errorDescription:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSPropertyListSerialization",
        b"dataWithPropertyList:format:options:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"NSPropertyListSerialization",
        b"propertyList:isValidForFormat:",
        {"retval": {"type": "Z"}},
    )
    r(
        b"NSPropertyListSerialization",
        b"propertyListFromData:mutabilityOption:format:errorDescription:",
        {"arguments": {4: {"type_modifier": b"o"}, 5: {"type_modifier": b"o"}}},
    )
    r(
        b"NSPropertyListSerialization",
        b"propertyListWithData:options:format:error:",
        {"arguments": {4: {"type_modifier": b"o"}, 5: {"type_modifier": b"o"}}},
    )
    r(
        b"NSPropertyListSerialization",
        b"propertyListWithStream:options:format:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"NSPropertyListSerialization",
        b"writePropertyList:toStream:format:options:error:",
        {"arguments": {6: {"type_modifier": b"o"}}},
    )
    r(b"NSProxy", b"allowsWeakReference", {"retval": {"type": "Z"}})
    r(b"NSProxy", b"methodSignatureForSelector:", {"arguments": {2: {"type": ":"}}})
    r(
        b"NSProxy",
        b"respondsToSelector:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type": ":"}}},
    )
    r(b"NSProxy", b"retainWeakReference", {"retval": {"type": "Z"}})
    r(b"NSRecursiveLock", b"lockBeforeDate:", {"retval": {"type": "Z"}})
    r(b"NSRecursiveLock", b"tryLock", {"retval": {"type": "Z"}})
    r(
        b"NSRegularExpression",
        b"enumerateMatchesInString:options:range:usingBlock:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Q"},
                            3: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSRegularExpression",
        b"initWithPattern:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSRegularExpression",
        b"regularExpressionWithPattern:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSRunLoop",
        b"cancelPerformSelector:target:argument:",
        {"arguments": {2: {"type": ":", "sel_of_type": b"v@:@"}}},
    )
    r(
        b"NSRunLoop",
        b"performBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSRunLoop",
        b"performInModes:block:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSRunLoop",
        b"performSelector:target:argument:order:modes:",
        {"arguments": {2: {"sel_of_type": b"v@:@"}}},
    )
    r(b"NSRunLoop", b"runMode:beforeDate:", {"retval": {"type": "Z"}})
    r(b"NSScanner", b"caseSensitive", {"retval": {"type": "Z"}})
    r(b"NSScanner", b"isAtEnd", {"retval": {"type": "Z"}})
    r(
        b"NSScanner",
        b"scanCharactersFromSet:intoString:",
        {
            "retval": {"type": "Z"},
            "arguments": {3: {"null_accepted": False, "type_modifier": b"o"}},
        },
    )
    r(
        b"NSScanner",
        b"scanDecimal:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {
                    "null_accepted": False,
                    "type": b"^{NSDecimal=b8b4b1b1b18[8S]}",
                    "type_modifier": b"o",
                }
            },
        },
    )
    r(
        b"NSScanner",
        b"scanDouble:",
        {
            "retval": {"type": "Z"},
            "arguments": {2: {"null_accepted": False, "type_modifier": b"o"}},
        },
    )
    r(
        b"NSScanner",
        b"scanFloat:",
        {
            "retval": {"type": "Z"},
            "arguments": {2: {"null_accepted": False, "type_modifier": b"o"}},
        },
    )
    r(
        b"NSScanner",
        b"scanHexDouble:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"null_accepted": False, "type": "^d", "type_modifier": b"o"}
            },
        },
    )
    r(
        b"NSScanner",
        b"scanHexFloat:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"null_accepted": False, "type": "^f", "type_modifier": b"o"}
            },
        },
    )
    r(
        b"NSScanner",
        b"scanHexInt:",
        {
            "retval": {"type": "Z"},
            "arguments": {2: {"null_accepted": False, "type_modifier": b"o"}},
        },
    )
    r(
        b"NSScanner",
        b"scanHexLongLong:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"null_accepted": False, "type": "^Q", "type_modifier": b"o"}
            },
        },
    )
    r(
        b"NSScanner",
        b"scanInt:",
        {
            "retval": {"type": "Z"},
            "arguments": {2: {"null_accepted": False, "type_modifier": b"o"}},
        },
    )
    r(
        b"NSScanner",
        b"scanInteger:",
        {
            "retval": {"type": "Z"},
            "arguments": {2: {"null_accepted": False, "type_modifier": b"o"}},
        },
    )
    r(
        b"NSScanner",
        b"scanLongLong:",
        {
            "retval": {"type": "Z"},
            "arguments": {2: {"null_accepted": False, "type_modifier": b"o"}},
        },
    )
    r(
        b"NSScanner",
        b"scanString:intoString:",
        {
            "retval": {"type": "Z"},
            "arguments": {3: {"null_accepted": False, "type_modifier": b"o"}},
        },
    )
    r(
        b"NSScanner",
        b"scanUnsignedLongLong:",
        {
            "retval": {"type": "Z"},
            "arguments": {2: {"null_accepted": False, "type_modifier": b"o"}},
        },
    )
    r(
        b"NSScanner",
        b"scanUpToCharactersFromSet:intoString:",
        {
            "retval": {"type": "Z"},
            "arguments": {3: {"null_accepted": False, "type_modifier": b"o"}},
        },
    )
    r(
        b"NSScanner",
        b"scanUpToString:intoString:",
        {
            "retval": {"type": "Z"},
            "arguments": {3: {"null_accepted": False, "type_modifier": b"o"}},
        },
    )
    r(b"NSScanner", b"setCaseSensitive:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"NSScriptClassDescription",
        b"hasOrderedToManyRelationshipForKey:",
        {"retval": {"type": "Z"}},
    )
    r(b"NSScriptClassDescription", b"hasPropertyForKey:", {"retval": {"type": "Z"}})
    r(
        b"NSScriptClassDescription",
        b"hasReadablePropertyForKey:",
        {"retval": {"type": "Z"}},
    )
    r(
        b"NSScriptClassDescription",
        b"hasWritablePropertyForKey:",
        {"retval": {"type": "Z"}},
    )
    r(
        b"NSScriptClassDescription",
        b"isLocationRequiredToCreateForKey:",
        {"retval": {"type": "Z"}},
    )
    r(b"NSScriptClassDescription", b"isReadOnlyKey:", {"retval": {"type": "Z"}})
    r(b"NSScriptClassDescription", b"matchesAppleEventCode:", {"retval": {"type": "Z"}})
    r(b"NSScriptClassDescription", b"supportsCommand:", {"retval": {"type": "Z"}})
    r(
        b"NSScriptCoercionHandler",
        b"registerCoercer:selector:toConvertFromClass:toClass:",
        {"arguments": {3: {"sel_of_type": b"@@:@#"}}},
    )
    r(b"NSScriptCommand", b"isWellFormed", {"retval": {"type": "Z"}})
    r(
        b"NSScriptCommandDescription",
        b"isOptionalArgumentWithName:",
        {"retval": {"type": "Z"}},
    )
    r(
        b"NSScriptObjectSpecifier",
        b"containerIsObjectBeingTested",
        {"retval": {"type": "Z"}},
    )
    r(
        b"NSScriptObjectSpecifier",
        b"containerIsRangeContainerObject",
        {"retval": {"type": "Z"}},
    )
    r(
        b"NSScriptObjectSpecifier",
        b"indicesOfObjectsByEvaluatingWithContainer:count:",
        {
            "retval": {"c_array_length_in_arg": 3},
            "arguments": {3: {"type_modifier": b"o"}},
        },
    )
    r(
        b"NSScriptObjectSpecifier",
        b"setContainerIsObjectBeingTested:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSScriptObjectSpecifier",
        b"setContainerIsRangeContainerObject:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"NSScriptWhoseTest", b"isTrue", {"retval": {"type": "Z"}})
    r(
        b"NSSet",
        b"addObserver:forKeyPath:options:context:",
        {"arguments": {5: {"type": "^v"}}},
    )
    r(b"NSSet", b"containsObject:", {"retval": {"type": "Z"}})
    r(
        b"NSSet",
        b"enumerateObjectsUsingBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSSet",
        b"enumerateObjectsWithOptions:usingBlock:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSSet",
        b"initWithObjects:",
        {"c_array_delimited_by_null": True, "variadic": True},
    )
    r(
        b"NSSet",
        b"initWithObjects:count:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(b"NSSet", b"initWithSet:copyItems:", {"arguments": {3: {"type": "Z"}}})
    r(b"NSSet", b"intersectsSet:", {"retval": {"type": "Z"}})
    r(b"NSSet", b"isEqualToSet:", {"retval": {"type": "Z"}})
    r(b"NSSet", b"isSubsetOfSet:", {"retval": {"type": "Z"}})
    r(
        b"NSSet",
        b"makeObjectsPerformSelector:",
        {"arguments": {2: {"sel_of_type": b"v@:"}}},
    )
    r(
        b"NSSet",
        b"makeObjectsPerformSelector:withObject:",
        {"arguments": {2: {"sel_of_type": b"v@:@"}}},
    )
    r(
        b"NSSet",
        b"objectsPassingTest:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSSet",
        b"objectsWithOptions:passingTest:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSSet",
        b"setWithObjects:",
        {"c_array_delimited_by_null": True, "variadic": True},
    )
    r(
        b"NSSet",
        b"setWithObjects:count:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(b"NSSocketPortNameServer", b"registerPort:name:", {"retval": {"type": "Z"}})
    r(
        b"NSSocketPortNameServer",
        b"registerPort:name:nameServerPortNumber:",
        {"retval": {"type": "Z"}},
    )
    r(b"NSSocketPortNameServer", b"removePortForName:", {"retval": {"type": "Z"}})
    r(b"NSSortDescriptor", b"ascending", {"retval": {"type": "Z"}})
    r(
        b"NSSortDescriptor",
        b"comparator",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"i"},
                    "arguments": {
                        0: {"type": b"^v"},
                        1: {"type": b"@"},
                        2: {"type": b"@"},
                    },
                }
            }
        },
    )
    r(b"NSSortDescriptor", b"initWithKey:ascending:", {"arguments": {3: {"type": "Z"}}})
    r(
        b"NSSortDescriptor",
        b"initWithKey:ascending:comparator:",
        {
            "arguments": {
                3: {"type": "Z"},
                4: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"NSSortDescriptor",
        b"initWithKey:ascending:selector:",
        {"arguments": {3: {"type": "Z"}, 4: {"sel_of_type": b"i@:@"}}},
    )
    r(
        b"NSSortDescriptor",
        b"sortDescriptorWithKey:ascending:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSSortDescriptor",
        b"sortDescriptorWithKey:ascending:comparator:",
        {
            "arguments": {
                3: {"type": "Z"},
                4: {
                    "callable": {
                        "retval": {"type": b"i"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"NSSortDescriptor",
        b"sortDescriptorWithKey:ascending:selector:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSSpellServer",
        b"isWordInUserDictionaries:caseSensitive:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type": "Z"}}},
    )
    r(b"NSSpellServer", b"registerLanguage:byVendor:", {"retval": {"type": "Z"}})
    r(
        b"NSStream",
        b"getBoundStreamsWithBufferSize:inputStream:outputStream:",
        {"arguments": {3: {"type_modifier": b"o"}, 4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSStream",
        b"getStreamsToHost:port:inputStream:outputStream:",
        {
            "arguments": {
                4: {"null_accepted": False, "type_modifier": b"o"},
                5: {"null_accepted": False, "type_modifier": b"o"},
            }
        },
    )
    r(
        b"NSStream",
        b"getStreamsToHostWithName:port:inputStream:outputStream:",
        {"arguments": {4: {"type_modifier": b"o"}, 5: {"type_modifier": b"o"}}},
    )
    r(b"NSStream", b"setProperty:forKey:", {"retval": {"type": "Z"}})
    r(b"NSString", b"", {"retval": {"type": "*"}})
    r(
        b"NSString",
        b"UTF8String",
        {"retval": {"c_array_delimited_by_null": True, "type": "^t"}},
    )
    r(
        b"NSString",
        b"availableStringEncodings",
        {"retval": {"c_array_delimited_by_null": True, "type": "r^Q"}},
    )
    r(b"NSString", b"boolValue", {"retval": {"type": "Z"}})
    r(
        b"NSString",
        b"cString",
        {"retval": {"c_array_delimited_by_null": True, "type": "^t"}},
    )
    r(
        b"NSString",
        b"cStringUsingEncoding:",
        {"retval": {"c_array_delimited_by_null": True, "type": "^v"}},
    )
    r(b"NSString", b"canBeConvertedToEncoding:", {"retval": {"type": "Z"}})
    r(b"NSString", b"characterAtIndex:", {"retval": {"type": "T"}})
    r(
        b"NSString",
        b"compare:options:range:",
        {"arguments": {4: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSString",
        b"compare:options:range:locale:",
        {"arguments": {4: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSString",
        b"completePathIntoString:caseSensitive:matchesIntoArray:filterTypes:",
        {
            "arguments": {
                2: {"type_modifier": b"o"},
                3: {"type": "Z"},
                4: {"type_modifier": b"o"},
            }
        },
    )
    r(b"NSString", b"containsString:", {"retval": {"type": b"Z"}})
    r(
        b"NSString",
        b"dataUsingEncoding:allowLossyConversion:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSString",
        b"enumerateLinesUsingBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSString",
        b"enumerateLinguisticTagsInRange:scheme:options:orthography:usingBlock:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"{_NSRange=QQ}"},
                            3: {"type": b"{_NSRange=QQ}"},
                            4: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSString",
        b"enumerateSubstringsInRange:options:usingBlock:",
        {
            "arguments": {
                2: {"type": "{_NSRange=QQ}"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"{_NSRange=QQ}"},
                            3: {"type": b"{_NSRange=QQ}"},
                            4: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                },
            }
        },
    )
    r(
        b"NSString",
        b"enumeratorLinguisticTagsInRange:scheme:options:orthography:usingBlock:",
        {
            "arguments": {
                6: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"{_NSRange=QQ}"},
                            3: {"type": b"{_NSRange=QQ}"},
                            4: {"type": b"^Z", "type_modifier": "o"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSString",
        b"fileSystemRepresentation",
        {"retval": {"c_array_delimited_by_null": True, "type": "^t"}},
    )
    r(
        b"NSString",
        b"getBytes:maxLength:usedLength:encoding:options:range:remainingRange:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {
                    "type": "^v",
                    "type_modifier": b"o",
                    "c_array_length_in_arg": (3, 4),
                },
                4: {"type": "^Q", "type_modifier": b"o"},
                7: {"type": "{_NSRange=QQ}"},
                8: {"type": "^{_NSRange=QQ}", "type_modifier": b"o"},
            },
            "suggestion": "do not use",
        },
    )
    r(
        b"NSString",
        b"getCString:",
        {"arguments": {2: {"type": "*"}}, "suggestion": "use -cString"},
    )
    r(
        b"NSString",
        b"getCString:maxLength:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": "^v", "type_modifier": b"o", "c_array_length_in_arg": 3}
            },
            "suggestion": "use -cString instead",
        },
    )
    r(
        b"NSString",
        b"getCString:maxLength:encoding:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": "^v", "type_modifier": b"o", "c_array_length_in_arg": 3}
            },
            "suggestion": "use -cString instead",
        },
    )
    r(
        b"NSString",
        b"getCString:maxLength:range:remainingRange:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": "^v", "type_modifier": b"o", "c_array_length_in_arg": 3},
                5: {"type": "^{_NSRange=QQ}", "type_modifier": b"o"},
            },
            "suggestion": "use -cString instead",
        },
    )
    r(
        b"NSString",
        b"getCharacters:",
        {
            "retval": {"type": "v"},
            "arguments": {
                2: {
                    "type": "^T",
                    "type_modifier": b"o",
                    "c_array_of_variable_length": True,
                }
            },
        },
    )
    r(
        b"NSString",
        b"getCharacters:range:",
        {
            "retval": {"type": "v"},
            "arguments": {
                2: {"type": "^T", "type_modifier": b"o", "c_array_length_in_arg": 3},
                3: {"type": "{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSString",
        b"getFileSystemRepresentation:maxLength:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": "^t", "type_modifier": b"o", "c_array_length_in_arg": 3}
            },
        },
    )
    r(
        b"NSString",
        b"getLineStart:end:contentsEnd:forRange:",
        {
            "retval": {"type": "v"},
            "arguments": {
                2: {"type_modifier": b"o"},
                3: {"type_modifier": b"o"},
                4: {"type_modifier": b"o"},
                5: {"type": "{_NSRange=QQ}"},
            },
        },
    )
    r(
        b"NSString",
        b"getParagraphStart:end:contentsEnd:forRange:",
        {
            "retval": {"type": "v"},
            "arguments": {
                2: {"type_modifier": b"o"},
                3: {"type_modifier": b"o"},
                4: {"type_modifier": b"o"},
                5: {"type": "{_NSRange=QQ}"},
            },
        },
    )
    r(b"NSString", b"hasPrefix:", {"retval": {"type": "Z"}})
    r(b"NSString", b"hasSuffix:", {"retval": {"type": "Z"}})
    r(
        b"NSString",
        b"initWithBytes:length:encoding:",
        {"arguments": {2: {"type_modifier": b"n", "c_array_length_in_arg": 3}}},
    )
    r(
        b"NSString",
        b"initWithBytesNoCopy:length:encoding:deallocator:",
        {
            "arguments": {
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"^v"},
                            2: {"type": b"Q"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSString",
        b"initWithBytesNoCopy:length:encoding:freeWhenDone:",
        {
            "arguments": {
                2: {"type": "^v", "type_modifier": b"n", "c_array_length_in_arg": 3},
                5: {"type": "Z"},
            },
            "suggestion": "use -initWithBytes:length:encoding instead",
        },
    )
    r(
        b"NSString",
        b"initWithCString:",
        {
            "arguments": {
                2: {
                    "c_array_delimited_by_null": True,
                    "type": "^v",
                    "type_modifier": b"n",
                }
            }
        },
    )
    r(
        b"NSString",
        b"initWithCString:encoding:",
        {
            "arguments": {
                2: {
                    "c_array_delimited_by_null": True,
                    "type": "^t",
                    "type_modifier": b"n",
                }
            }
        },
    )
    r(
        b"NSString",
        b"initWithCString:length:",
        {
            "arguments": {
                2: {"type": "^v", "type_modifier": b"n", "c_array_length_in_arg": 3}
            }
        },
    )
    r(
        b"NSString",
        b"initWithCStringNoCopy:length:freeWhenDone:",
        {
            "arguments": {
                2: {"type": "^v", "type_modifier": b"n", "c_array_length_in_arg": 3},
                4: {"type": "Z"},
            },
            "suggestion": "use -initWithCString:length: instead",
        },
    )
    r(
        b"NSString",
        b"initWithCharacters:length:",
        {
            "arguments": {
                2: {"type": "^T", "type_modifier": b"n", "c_array_length_in_arg": 3}
            }
        },
    )
    r(
        b"NSString",
        b"initWithCharactersNoCopy:length:deallocator:",
        {
            "retval": {"type": "@"},
            "arguments": {
                2: {"type": "^T", "type_modifier": b"n", "c_array_length_in_arg": 3},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {
                                "type": b"^T",
                                "type_modifier": "n",
                                "c_array_length_in_arg": 2,
                            },
                            2: {"type": b"Q"},
                        },
                    }
                },
            },
            "suggestion": "use -initWithCharacters:length: instead",
        },
    )
    r(
        b"NSString",
        b"initWithCharactersNoCopy:length:freeWhenDone:",
        {
            "retval": {"type": "@"},
            "arguments": {
                2: {"type": "^T", "type_modifier": b"n", "c_array_length_in_arg": 3},
                4: {"type": "Z"},
            },
            "suggestion": "use -initWithCharacters:length: instead",
        },
    )
    r(
        b"NSString",
        b"initWithContentsOfFile:encoding:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSString",
        b"initWithContentsOfFile:usedEncoding:error:",
        {
            "arguments": {
                3: {"type": "r^Q", "type_modifier": b"o"},
                4: {"type_modifier": b"o"},
            }
        },
    )
    r(b"NSString", b"initWithContentsOfURL:", {"arguments": {2: {"type": "@"}}})
    r(
        b"NSString",
        b"initWithContentsOfURL:encoding:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSString",
        b"initWithContentsOfURL:usedEncoding:error:",
        {
            "arguments": {
                3: {"type": "r^Q", "type_modifier": b"o"},
                4: {"type_modifier": b"o"},
            }
        },
    )
    r(
        b"NSString",
        b"initWithFormat:",
        {"arguments": {2: {"printf_format": True, "type": "@"}}, "variadic": True},
    )
    r(
        b"NSString",
        b"initWithFormat:arguments:",
        {
            "arguments": {3: {"type": "[1{?=II^v^v}]"}},
            "suggestion": "use -initWithFormat:",
        },
    )
    r(
        b"NSString",
        b"initWithFormat:locale:",
        {"arguments": {2: {"printf_format": True, "type": "@"}}, "variadic": True},
    )
    r(
        b"NSString",
        b"initWithFormat:locale:arguments:",
        {
            "arguments": {4: {"type": "[1{?=II^v^v}]"}},
            "suggestion": "use -initWithFormat:locale:",
        },
    )
    r(
        b"NSString",
        b"initWithUTF8String:",
        {
            "arguments": {
                2: {
                    "c_array_delimited_by_null": True,
                    "type": "^t",
                    "type_modifier": b"n",
                }
            }
        },
    )
    r(
        b"NSString",
        b"initWithValidatedFormat:validFormatSpecifiers:arguments:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"NSString",
        b"initWithValidatedFormat:validFormatSpecifiers:error:",
        {
            "arguments": {2: {"printf_format": True}, 4: {"type_modifier": b"o"}},
            "variadic": True,
        },
    )
    r(
        b"NSString",
        b"initWithValidatedFormat:validFormatSpecifiers:locale:arguments:error:",
        {"arguments": {6: {"type_modifier": b"o"}}},
    )
    r(
        b"NSString",
        b"initWithValidatedFormat:validFormatSpecifiers:locale:error:",
        {
            "arguments": {2: {"printf_format": True}, 5: {"type_modifier": b"o"}},
            "variadic": True,
        },
    )
    r(
        b"NSString",
        b"initWithValidatedFormat:validatedFormatSpecifiers:arguments:error:",
        {
            "arguments": {5: {"type_modifier": b"o"}},
            "suggestion": "use -initWithValidatedFormat:validatedFormatSpecifiers:error:",
        },
    )
    r(
        b"NSString",
        b"initWithValidatedFormat:validatedFormatSpecifiers:error:",
        {"arguments": {2: {"printf_format": True}, 4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSString",
        b"initWithValidatedFormat:validatedFormatSpecifiers:locale:arguments:error:",
        {
            "arguments": {6: {"type_modifier": b"o"}},
            "suggestion": "use -initWithValidatedFormat:validatedFormatSpecifiers:locale:error:",
        },
    )
    r(
        b"NSString",
        b"initWithValidatedFormat:validatedFormatSpecifiers:locale:error:",
        {"arguments": {2: {"printf_format": True}, 5: {"type_modifier": b"o"}}},
    )
    r(b"NSString", b"isAbsolutePath", {"retval": {"type": "Z"}})
    r(b"NSString", b"isEqualToString:", {"retval": {"type": "Z"}})
    r(
        b"NSString",
        b"lineRangeForRange:",
        {
            "retval": {"type": "{_NSRange=QQ}"},
            "arguments": {2: {"type": "{_NSRange=QQ}"}},
        },
    )
    r(
        b"NSString",
        b"linguisticTagsInRange:scheme:options:orthography:tokenRanges:",
        {"arguments": {6: {"type_modifier": b"o"}}},
    )
    r(
        b"NSString",
        b"localizedCaseInsensitiveContainsString:",
        {"retval": {"type": b"Z"}},
    )
    r(b"NSString", b"localizedStandardContainsString:", {"retval": {"type": "Z"}})
    r(
        b"NSString",
        b"localizedStringWithFormat:",
        {"arguments": {2: {"printf_format": True, "type": "@"}}, "variadic": True},
    )
    r(
        b"NSString",
        b"localizedStringWithValidatedFormat:validFormatSpecifiers:error:",
        {"arguments": {4: {"type_modifier": b"o"}}, "variadic": True},
    )
    r(
        b"NSString",
        b"lossyCString",
        {"retval": {"c_array_delimited_by_null": True, "type": "^t"}},
    )
    r(
        b"NSString",
        b"paragraphRangeForRange:",
        {
            "retval": {"type": "{_NSRange=QQ}"},
            "arguments": {2: {"type": "{_NSRange=QQ}"}},
        },
    )
    r(b"NSString", b"rangeOfCharacterFromSet:", {"retval": {"type": "{_NSRange=QQ}"}})
    r(
        b"NSString",
        b"rangeOfCharacterFromSet:options:",
        {"retval": {"type": "{_NSRange=QQ}"}},
    )
    r(
        b"NSString",
        b"rangeOfCharacterFromSet:options:range:",
        {
            "retval": {"type": "{_NSRange=QQ}"},
            "arguments": {4: {"type": "{_NSRange=QQ}"}},
        },
    )
    r(
        b"NSString",
        b"rangeOfComposedCharacterSequenceAtIndex:",
        {"retval": {"type": "{_NSRange=QQ}"}},
    )
    r(
        b"NSString",
        b"rangeOfComposedCharacterSequencesForRange:",
        {
            "retval": {"type": "{_NSRange=QQ}"},
            "arguments": {2: {"type": "{_NSRange=QQ}"}},
        },
    )
    r(b"NSString", b"rangeOfString:", {"retval": {"type": "{_NSRange=QQ}"}})
    r(b"NSString", b"rangeOfString:options:", {"retval": {"type": "{_NSRange=QQ}"}})
    r(
        b"NSString",
        b"rangeOfString:options:range:",
        {
            "retval": {"type": "{_NSRange=QQ}"},
            "arguments": {4: {"type": "{_NSRange=QQ}"}},
        },
    )
    r(
        b"NSString",
        b"rangeOfString:options:range:locale:",
        {
            "retval": {"type": "{_NSRange=QQ}"},
            "arguments": {4: {"type": "{_NSRange=QQ}"}},
        },
    )
    r(
        b"NSString",
        b"stringByAppendingFormat:",
        {"arguments": {2: {"printf_format": True, "type": "@"}}, "variadic": True},
    )
    r(
        b"NSString",
        b"stringByApplyingTransform:reverse:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSString",
        b"stringByReplacingCharactersInRange:withString:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSString",
        b"stringByReplacingOccurrencesOfString:withString:options:range:",
        {"arguments": {5: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSString",
        b"stringEncodingForData:encodingOptions:convertedString:usedLossyConversion:",
        {
            "arguments": {
                4: {"type_modifier": b"o"},
                5: {"type": b"^Z", "type_modifier": b"o"},
            }
        },
    )
    r(
        b"NSString",
        b"stringWithCString:",
        {
            "arguments": {
                2: {
                    "c_array_delimited_by_null": True,
                    "type": "^v",
                    "type_modifier": b"n",
                }
            }
        },
    )
    r(
        b"NSString",
        b"stringWithCString:encoding:",
        {
            "arguments": {
                2: {
                    "c_array_delimited_by_null": True,
                    "type": "^t",
                    "type_modifier": b"n",
                }
            }
        },
    )
    r(
        b"NSString",
        b"stringWithCString:length:",
        {
            "arguments": {
                2: {"type": "^v", "type_modifier": b"n", "c_array_length_in_arg": 3}
            }
        },
    )
    r(
        b"NSString",
        b"stringWithCharacters:length:",
        {
            "arguments": {
                2: {"type": "r^T", "type_modifier": b"n", "c_array_length_in_arg": 3}
            }
        },
    )
    r(
        b"NSString",
        b"stringWithContentsOfFile:encoding:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSString",
        b"stringWithContentsOfFile:usedEncoding:error:",
        {
            "arguments": {
                3: {"type": "r^Q", "type_modifier": b"o"},
                4: {"type_modifier": b"o"},
            }
        },
    )
    r(
        b"NSString",
        b"stringWithContentsOfURL:encoding:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSString",
        b"stringWithContentsOfURL:usedEncoding:error:",
        {
            "arguments": {
                3: {"type": "r^Q", "type_modifier": b"o"},
                4: {"type_modifier": b"o"},
            }
        },
    )
    r(
        b"NSString",
        b"stringWithFormat:",
        {"arguments": {2: {"printf_format": True, "type": "@"}}, "variadic": True},
    )
    r(
        b"NSString",
        b"stringWithUTF8String:",
        {
            "arguments": {
                2: {
                    "c_array_delimited_by_null": True,
                    "type": "^t",
                    "type_modifier": b"n",
                }
            }
        },
    )
    r(
        b"NSString",
        b"stringWithValidatedFormat:validFormatSpecifiers:error:",
        {
            "arguments": {2: {"printf_format": True}, 4: {"type_modifier": b"o"}},
            "variadic": True,
        },
    )
    r(
        b"NSString",
        b"stringWithValidatedFormat:validFormatSpecifiers:locale:error:",
        {"arguments": {2: {"printf_format": True}, 5: {"type_modifier": b"o"}}},
    )
    r(
        b"NSString",
        b"stringWithValidatedFormat:validatedFormatSpecifiers:error:",
        {"arguments": {2: {"printf_format": True}, 4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSString",
        b"stringWithValidatedFormat:validatedFormatSpecifiers:locale:error:",
        {"arguments": {2: {"printf_format": True}, 5: {"type_modifier": b"o"}}},
    )
    r(
        b"NSString",
        b"substringWithRange:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSString",
        b"writeToFile:atomically:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSString",
        b"writeToFile:atomically:encoding:error:",
        {
            "retval": {"type": "Z"},
            "arguments": {3: {"type": "Z"}, 5: {"type_modifier": b"o"}},
        },
    )
    r(
        b"NSString",
        b"writeToURL:atomically:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSString",
        b"writeToURL:atomically:encoding:error:",
        {
            "retval": {"type": "Z"},
            "arguments": {3: {"type": "Z"}, 5: {"type_modifier": b"o"}},
        },
    )
    r(b"NSTask", b"isRunning", {"retval": {"type": "Z"}})
    r(
        b"NSTask",
        b"launchAndReturnError:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"NSTask",
        b"launchedTaskWithExecutableURL:arguments:error:terminationHandler:",
        {
            "arguments": {
                4: {"type_modifier": b"o"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                },
            }
        },
    )
    r(b"NSTask", b"resume", {"retval": {"type": "Z"}})
    r(
        b"NSTask",
        b"setTerminationHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(b"NSTask", b"suspend", {"retval": {"type": "Z"}})
    r(
        b"NSTask",
        b"terminationHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                }
            }
        },
    )
    r(
        b"NSTextCheckingResult",
        b"addressCheckingResultWithRange:components:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSTextCheckingResult",
        b"correctionCheckingResultWithRange:replacementString:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSTextCheckingResult",
        b"dashCheckingResultWithRange:replacementString:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSTextCheckingResult",
        b"dateCheckingResultWithRange:date:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSTextCheckingResult",
        b"dateCheckingResultWithRange:date:timeZone:duration:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSTextCheckingResult",
        b"grammarCheckingResultWithRange:details:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSTextCheckingResult",
        b"linkCheckingResultWithRange:URL:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSTextCheckingResult",
        b"orthographyCheckingResultWithRange:orthography:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSTextCheckingResult",
        b"phoneNumberCheckingResultWithRange:phoneNumber:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSTextCheckingResult",
        b"quoteCheckingResultWithRange:replacementString:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSTextCheckingResult",
        b"regularExpressionCheckingResultWithRanges:count:regularExpression:",
        {
            "arguments": {
                2: {
                    "type": "^{_NSRange=QQ}",
                    "type_modifier": b"n",
                    "c_array_length_in_arg": 3,
                }
            }
        },
    )
    r(
        b"NSTextCheckingResult",
        b"replacementCheckingResultWithRange:replacementString:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSTextCheckingResult",
        b"spellCheckingResultWithRange:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSTextCheckingResult",
        b"transitInformationCheckingResultWithRange:components:",
        {"arguments": {2: {"type": "{_NSRange=QQ}"}}},
    )
    r(
        b"NSThread",
        b"detachNewThreadSelector:toTarget:withObject:",
        {"arguments": {2: {"sel_of_type": b"v@:@"}}},
    )
    r(
        b"NSThread",
        b"detachNewThreadWithBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSThread",
        b"initWithBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSThread",
        b"initWithTarget:selector:object:",
        {"arguments": {3: {"sel_of_type": b"v@:@"}}},
    )
    r(b"NSThread", b"isCancelled", {"retval": {"type": "Z"}})
    r(b"NSThread", b"isExecuting", {"retval": {"type": "Z"}})
    r(b"NSThread", b"isFinished", {"retval": {"type": "Z"}})
    r(b"NSThread", b"isMainThread", {"retval": {"type": "Z"}})
    r(b"NSThread", b"isMultiThreaded", {"retval": {"type": "Z"}})
    r(b"NSThread", b"setThreadPriority:", {"retval": {"type": "Z"}})
    r(b"NSTimeZone", b"isDaylightSavingTime", {"retval": {"type": "Z"}})
    r(b"NSTimeZone", b"isDaylightSavingTimeForDate:", {"retval": {"type": "Z"}})
    r(b"NSTimeZone", b"isEqualToTimeZone:", {"retval": {"type": "Z"}})
    r(
        b"NSTimer",
        b"initWithFireDate:interval:repeats:block:",
        {
            "arguments": {
                4: {"type": "Z"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                },
            }
        },
    )
    r(
        b"NSTimer",
        b"initWithFireDate:interval:target:selector:userInfo:repeats:",
        {"arguments": {5: {"sel_of_type": b"v@:@"}, 7: {"type": "Z"}}},
    )
    r(b"NSTimer", b"isValid", {"retval": {"type": "Z"}})
    r(
        b"NSTimer",
        b"scheduledTimerWithTimeInterval:invocation:repeats:",
        {"arguments": {4: {"type": "Z"}}},
    )
    r(
        b"NSTimer",
        b"scheduledTimerWithTimeInterval:repeats:block:",
        {
            "arguments": {
                3: {"type": b"Z"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                },
            }
        },
    )
    r(
        b"NSTimer",
        b"scheduledTimerWithTimeInterval:target:selector:userInfo:repeats:",
        {"arguments": {4: {"sel_of_type": b"v@:@"}, 6: {"type": "Z"}}},
    )
    r(
        b"NSTimer",
        b"timerWithTimeInterval:invocation:repeats:",
        {"arguments": {4: {"type": "Z"}}},
    )
    r(
        b"NSTimer",
        b"timerWithTimeInterval:repeats:block:",
        {
            "arguments": {
                3: {"type": "Z"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                },
            }
        },
    )
    r(
        b"NSTimer",
        b"timerWithTimeInterval:target:selector:userInfo:repeats:",
        {"arguments": {4: {"sel_of_type": b"v@:@"}, 6: {"type": "Z"}}},
    )
    r(
        b"NSURL",
        b"URLByAppendingPathComponent:isDirectory:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSURL",
        b"URLByResolvingAliasFileAtURL:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSURL",
        b"URLByResolvingBookmarkData:options:relativeToURL:bookmarkDataIsStale:error:",
        {
            "arguments": {
                5: {"type": "^Z", "type_modifier": b"o"},
                6: {"type_modifier": b"o"},
            }
        },
    )
    r(b"NSURL", b"URLHandleUsingCache:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"NSURL",
        b"URLWithString:encodingInvalidCharacters:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"NSURL",
        b"bookmarkDataWithContentsOfURL:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSURL",
        b"bookmarkDataWithOptions:includingResourceValuesForKeys:relativeToURL:error:",
        {"arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"NSURL",
        b"checkPromisedItemIsReachableAndReturnError:",
        {"retval": {"type": b"Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"NSURL",
        b"checkResourceIsReachableAndReturnError:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"NSURL",
        b"fileURLWithFileSystemRepresentation:isDirectory:relativeToURL:",
        {
            "arguments": {
                2: {
                    "c_array_delimited_by_null": True,
                    "type": "^t",
                    "type_modifier": b"n",
                },
                3: {"type": "Z"},
            }
        },
    )
    r(b"NSURL", b"fileURLWithPath:isDirectory:", {"arguments": {3: {"type": "Z"}}})
    r(
        b"NSURL",
        b"fileURLWithPath:isDirectory:relativeToURL:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSURL",
        b"getFileSystemRepresentation:maxLength:",
        {
            "retval": {"type": "Z"},
            "arguments": {
                2: {"type": "^t", "type_modifier": b"o", "c_array_length_in_arg": 3}
            },
        },
    )
    r(
        b"NSURL",
        b"getPromisedItemResourceValue:forKey:error:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSURL",
        b"getResourceValue:forKey:error:",
        {
            "retval": {"type": "Z"},
            "arguments": {2: {"type_modifier": b"o"}, 4: {"type_modifier": b"o"}},
        },
    )
    r(b"NSURL", b"hasDirectoryPath", {"retval": {"type": "Z"}})
    r(
        b"NSURL",
        b"initByResolvingBookmarkData:options:relativeToURL:bookmarkDataIsStale:error:",
        {
            "arguments": {
                5: {"type": "^Z", "type_modifier": b"o"},
                6: {"type_modifier": b"o"},
            }
        },
    )
    r(
        b"NSURL",
        b"initFileURLWithFileSystemRepresentation:isDirectory:relativeToURL:",
        {
            "arguments": {
                2: {
                    "c_array_delimited_by_null": True,
                    "type": "^t",
                    "type_modifier": b"n",
                },
                3: {"type": "Z"},
            }
        },
    )
    r(b"NSURL", b"initFileURLWithPath:isDirectory:", {"arguments": {3: {"type": "Z"}}})
    r(
        b"NSURL",
        b"initFileURLWithPath:isDirectory:relativeToURL:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSURL",
        b"initWithContentsOfURL:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSURL",
        b"initWithString:encodingInvalidCharacters:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(b"NSURL", b"isFileReferenceURL", {"retval": {"type": "Z"}})
    r(b"NSURL", b"isFileURL", {"retval": {"type": "Z"}})
    r(
        b"NSURL",
        b"loadResourceDataNotifyingClient:usingCache:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(
        b"NSURL",
        b"promisedItemResourceValuesForKeys:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"NSURL", b"resourceDataUsingCache:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"NSURL",
        b"resourceValuesForKeys:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"NSURL", b"setProperty:forKey:", {"retval": {"type": "Z"}})
    r(b"NSURL", b"setResourceData:", {"retval": {"type": "Z"}})
    r(
        b"NSURL",
        b"setResourceValue:forKey:error:",
        {"retval": {"type": "Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSURL",
        b"setResourceValues:error:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(b"NSURL", b"startAccessingSecurityScopedResource", {"retval": {"type": b"Z"}})
    r(
        b"NSURL",
        b"writeBookmarkData:toURL:options:error:",
        {"retval": {"type": "Z"}, "arguments": {5: {"type_modifier": b"o"}}},
    )
    r(
        b"NSURL",
        b"writeToURL:error:",
        {"retval": {"type": "Z"}, "arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSURLCache",
        b"getCachedResponseForDataTask:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSURLComponents",
        b"componentsWithString:encodingInvalidCharacters:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"NSURLComponents",
        b"componentsWithURL:resolvingAgainstBaseURL:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"NSURLComponents",
        b"initWithString:encodingInvalidCharacters:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(
        b"NSURLComponents",
        b"initWithURL:resolvingAgainstBaseURL:",
        {"arguments": {3: {"type": b"Z"}}},
    )
    r(b"NSURLConnection", b"canHandleRequest:", {"retval": {"type": "Z"}})
    r(
        b"NSURLConnection",
        b"initWithRequest:delegate:startImmediately:",
        {"arguments": {4: {"type": "Z"}}},
    )
    r(
        b"NSURLConnection",
        b"sendAsynchronousRequest:queue:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSURLConnection",
        b"sendSynchronousRequest:returningResponse:error:",
        {"arguments": {3: {"type_modifier": b"o"}, 4: {"type_modifier": b"o"}}},
    )
    r(b"NSURLCredential", b"hasPassword", {"retval": {"type": "Z"}})
    r(
        b"NSURLCredentialStorage",
        b"getCredentialsForProtectionSpace:task:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSURLCredentialStorage",
        b"getDefaultCredentialForProtectionSpace:task:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSURLDownload",
        b"canResumeDownloadDecodedWithEncodingMIMEType:",
        {"retval": {"type": "Z"}},
    )
    r(b"NSURLDownload", b"deletesFileUponFailure", {"retval": {"type": "Z"}})
    r(
        b"NSURLDownload",
        b"setDeletesFileUponFailure:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSURLDownload",
        b"setDestination:allowOverwrite:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(b"NSURLHandle", b"canInitWithURL:", {"retval": {"type": "Z"}})
    r(b"NSURLHandle", b"didLoadBytes:loadComplete:", {"arguments": {3: {"type": "Z"}}})
    r(b"NSURLHandle", b"initWithURL:cached:", {"arguments": {3: {"type": "Z"}}})
    r(b"NSURLHandle", b"writeData:", {"retval": {"type": "Z"}})
    r(b"NSURLHandle", b"writeProperty:forKey:", {"retval": {"type": "Z"}})
    r(b"NSURLProtectionSpace", b"isProxy", {"retval": {"type": "Z"}})
    r(b"NSURLProtectionSpace", b"receivesCredentialSecurely", {"retval": {"type": "Z"}})
    r(b"NSURLProtocol", b"canInitWithRequest:", {"retval": {"type": "Z"}})
    r(b"NSURLProtocol", b"canInitWithTask:", {"retval": {"type": b"Z"}})
    r(b"NSURLProtocol", b"registerClass:", {"retval": {"type": "Z"}})
    r(
        b"NSURLProtocol",
        b"requestIsCacheEquivalent:toRequest:",
        {"retval": {"type": "Z"}},
    )
    r(b"NSURLRequest", b"HTTPShouldHandleCookies", {"retval": {"type": "Z"}})
    r(b"NSURLRequest", b"HTTPShouldUsePipelining", {"retval": {"type": "Z"}})
    r(b"NSURLRequest", b"allowsCellularAccess", {"retval": {"type": b"Z"}})
    r(b"NSURLRequest", b"allowsConstrainedNetworkAccess", {"retval": {"type": b"Z"}})
    r(b"NSURLRequest", b"allowsExpensiveNetworkAccess", {"retval": {"type": b"Z"}})
    r(b"NSURLRequest", b"allowsPersistentDNS", {"retval": {"type": b"Z"}})
    r(b"NSURLRequest", b"assumesHTTP3Capable", {"retval": {"type": "Z"}})
    r(b"NSURLRequest", b"requiresDNSSECValidation", {"retval": {"type": b"Z"}})
    r(b"NSURLRequest", b"supportsSecureCoding", {"retval": {"type": b"Z"}})
    r(
        b"NSURLSession",
        b"dataTaskWithRequest:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSURLSession",
        b"dataTaskWithURL:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSURLSession",
        b"downloadTaskWithRequest:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSURLSession",
        b"downloadTaskWithResumeData:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSURLSession",
        b"downloadTaskWithURL:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSURLSession",
        b"flushWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSURLSession",
        b"getAllTasksWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSURLSession",
        b"getTasksWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSURLSession",
        b"resetWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSURLSession",
        b"uploadTaskWithRequest:fromData:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSURLSession",
        b"uploadTaskWithRequest:fromFile:completionHandler:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSURLSession",
        b"uploadTaskWithResumeData:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(b"NSURLSessionConfiguration", b"HTTPShouldSetCookies", {"retval": {"type": b"Z"}})
    r(
        b"NSURLSessionConfiguration",
        b"HTTPShouldUsePipelining",
        {"retval": {"type": b"Z"}},
    )
    r(b"NSURLSessionConfiguration", b"allowsCellularAccess", {"retval": {"type": b"Z"}})
    r(
        b"NSURLSessionConfiguration",
        b"allowsConstrainedNetworkAccess",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"NSURLSessionConfiguration",
        b"allowsExpensiveNetworkAccess",
        {"retval": {"type": b"Z"}},
    )
    r(b"NSURLSessionConfiguration", b"isDiscretionary", {"retval": {"type": "Z"}})
    r(
        b"NSURLSessionConfiguration",
        b"requiresDNSSECValidation",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"NSURLSessionConfiguration",
        b"sessionSendsLaunchEvents",
        {"retval": {"type": b"Z"}},
    )
    r(
        b"NSURLSessionConfiguration",
        b"setAllowsCellularAccess:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSURLSessionConfiguration",
        b"setAllowsConstrainedNetworkAccess:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSURLSessionConfiguration",
        b"setAllowsExpensiveNetworkAccess:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSURLSessionConfiguration",
        b"setDiscretionary:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSURLSessionConfiguration",
        b"setHTTPShouldSetCookies:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSURLSessionConfiguration",
        b"setHTTPShouldUsePipelining:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSURLSessionConfiguration",
        b"setRequiresDNSSECValidation:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSURLSessionConfiguration",
        b"setSessionSendsLaunchEvents:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSURLSessionConfiguration",
        b"setShouldUseExtendedBackgroundIdleMode:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSURLSessionConfiguration",
        b"setWaitsForConnectivity:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSURLSessionConfiguration",
        b"shouldUseExtendedBackgroundIdleMode",
        {"retval": {"type": "Z"}},
    )
    r(b"NSURLSessionConfiguration", b"waitsForConnectivity", {"retval": {"type": "Z"}})
    r(
        b"NSURLSessionDownloadTask",
        b"cancelByProducingResumeData:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSURLSessionStreamTask",
        b"readDataOfMinLength:maxLength:timeout:completionHandler:",
        {
            "arguments": {
                2: {"type": "Q"},
                3: {"type": "Q"},
                4: {"type": "d"},
                5: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"Z"},
                            3: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                },
            }
        },
    )
    r(
        b"NSURLSessionStreamTask",
        b"writeData:timeout:completionHandler:",
        {
            "arguments": {
                3: {"type": "d"},
                4: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    },
                    "type": "@?",
                },
            }
        },
    )
    r(b"NSURLSessionTask", b"prefersIncrementalDelivery", {"retval": {"type": b"Z"}})
    r(
        b"NSURLSessionTask",
        b"setPrefersIncrementalDelivery:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"NSURLSessionTaskTransactionMetrics", b"isCellular", {"retval": {"type": b"Z"}})
    r(
        b"NSURLSessionTaskTransactionMetrics",
        b"isConstrained",
        {"retval": {"type": b"Z"}},
    )
    r(b"NSURLSessionTaskTransactionMetrics", b"isExpensive", {"retval": {"type": b"Z"}})
    r(b"NSURLSessionTaskTransactionMetrics", b"isMultipath", {"retval": {"type": b"Z"}})
    r(
        b"NSURLSessionTaskTransactionMetrics",
        b"isProxyConnection",
        {"retval": {"type": "Z"}},
    )
    r(
        b"NSURLSessionTaskTransactionMetrics",
        b"isReusedConnection",
        {"retval": {"type": "Z"}},
    )
    r(
        b"NSURLSessionUploadTask",
        b"cancelByProducingResumeData:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSURLSessionWebSocketTask",
        b"receiveMessageWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSURLSessionWebSocketTask",
        b"sendMessage:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSURLSessionWebSocketTask",
        b"sendPingWithPongReceiveHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSUUID",
        b"getUUIDBytes:",
        {
            "arguments": {
                2: {"c_array_of_fixed_length": 16, "type": "^t", "type_modifier": b"o"}
            }
        },
    )
    r(
        b"NSUUID",
        b"initWithUUIDBytes:",
        {
            "arguments": {
                2: {"c_array_of_fixed_length": 16, "type": "^t", "type_modifier": b"n"}
            }
        },
    )
    r(b"NSUbiquitousKeyValueStore", b"boolForKey:", {"retval": {"type": "Z"}})
    r(
        b"NSUbiquitousKeyValueStore",
        b"setBool:forKey:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"NSUbiquitousKeyValueStore", b"synchronize", {"retval": {"type": b"Z"}})
    r(b"NSUbiquitousKeyValueStore", b"synchronize:", {"retval": {"type": "Z"}})
    r(b"NSUnarchiver", b"isAtEnd", {"retval": {"type": "Z"}})
    r(b"NSUndoManager", b"canRedo", {"retval": {"type": "Z"}})
    r(b"NSUndoManager", b"canUndo", {"retval": {"type": "Z"}})
    r(b"NSUndoManager", b"groupsByEvent", {"retval": {"type": "Z"}})
    r(b"NSUndoManager", b"isRedoing", {"retval": {"type": "Z"}})
    r(b"NSUndoManager", b"isUndoRegistrationEnabled", {"retval": {"type": "Z"}})
    r(b"NSUndoManager", b"isUndoing", {"retval": {"type": "Z"}})
    r(b"NSUndoManager", b"redoActionIsDiscardable", {"retval": {"type": "Z"}})
    r(
        b"NSUndoManager",
        b"redoMenuTitleForUndoActionName:",
        {"arguments": {2: {"type": "@"}}},
    )
    r(
        b"NSUndoManager",
        b"registerUndoWithTarget:handler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSUndoManager",
        b"registerUndoWithTarget:selector:object:",
        {"arguments": {3: {"sel_of_type": b"v@:@"}}},
    )
    r(b"NSUndoManager", b"setActionIsDiscardable:", {"arguments": {2: {"type": "Z"}}})
    r(b"NSUndoManager", b"setGroupsByEvent:", {"arguments": {2: {"type": "Z"}}})
    r(b"NSUndoManager", b"undoActionIsDiscardable", {"retval": {"type": "Z"}})
    r(
        b"NSUserActivity",
        b"deleteAllSavedUserActivitiesWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSUserActivity",
        b"deleteSavedUserActivitiesWithPersistentIdentifiers:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSUserActivity",
        b"getContinuationStreamsWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                            3: {"type": b"@"},
                        },
                    },
                    "type": "@?",
                }
            }
        },
    )
    r(b"NSUserActivity", b"isEligibleForHandoff", {"retval": {"type": "Z"}})
    r(b"NSUserActivity", b"isEligibleForPrediction", {"retval": {"type": b"Z"}})
    r(b"NSUserActivity", b"isEligibleForPublicIndexing", {"retval": {"type": "Z"}})
    r(b"NSUserActivity", b"isEligibleForSearch", {"retval": {"type": "Z"}})
    r(b"NSUserActivity", b"needsSave", {"retval": {"type": b"Z"}})
    r(b"NSUserActivity", b"setEligibleForHandoff:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"NSUserActivity",
        b"setEligibleForPrediction:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(
        b"NSUserActivity",
        b"setEligibleForPublicIndexing:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"NSUserActivity", b"setEligibleForSearch:", {"arguments": {2: {"type": "Z"}}})
    r(b"NSUserActivity", b"setNeedsSave:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"NSUserActivity",
        b"setSupportsContinuationStreams:",
        {"arguments": {2: {"type": b"Z"}}},
    )
    r(b"NSUserActivity", b"supportsContinuationStreams", {"retval": {"type": b"Z"}})
    r(
        b"NSUserAppleScriptTask",
        b"executeWithAppleEvent:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"NSUserAutomatorTask",
        b"executeWithInput:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"@"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(b"NSUserDefaults", b"boolForKey:", {"retval": {"type": "Z"}})
    r(b"NSUserDefaults", b"objectIsForcedForKey:", {"retval": {"type": "Z"}})
    r(b"NSUserDefaults", b"objectIsForcedForKey:inDomain:", {"retval": {"type": "Z"}})
    r(b"NSUserDefaults", b"setBool:forKey:", {"arguments": {2: {"type": "Z"}}})
    r(b"NSUserDefaults", b"synchronize", {"retval": {"type": "Z"}})
    r(b"NSUserNotification", b"hasActionButton", {"retval": {"type": b"Z"}})
    r(b"NSUserNotification", b"hasReplyButton", {"retval": {"type": b"Z"}})
    r(b"NSUserNotification", b"isPresented", {"retval": {"type": b"Z"}})
    r(b"NSUserNotification", b"isRemote", {"retval": {"type": b"Z"}})
    r(b"NSUserNotification", b"setHasActionButton:", {"arguments": {2: {"type": b"Z"}}})
    r(b"NSUserNotification", b"setHasReplyButton:", {"arguments": {2: {"type": b"Z"}}})
    r(
        b"NSUserScriptTask",
        b"executeWithCompletionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSUserScriptTask",
        b"initWithURL:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSUserUnixTask",
        b"executeWithArguments:completionHandler:",
        {
            "arguments": {
                3: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSValue",
        b"getValue:",
        {"arguments": {2: {"type": "^v"}}, "suggestion": "use another method"},
    )
    r(
        b"NSValue",
        b"initWithBytes:objCType:",
        {
            "arguments": {
                2: {
                    "type": "^v",
                    "type_modifier": b"n",
                    "c_array_of_variable_length": True,
                },
                3: {
                    "c_array_delimited_by_null": True,
                    "type": "^t",
                    "type_modifier": b"n",
                },
            },
            "suggestion": "use something else",
        },
    )
    r(b"NSValue", b"isEqualToValue:", {"retval": {"type": "Z"}})
    r(
        b"NSValue",
        b"objCType",
        {"retval": {"c_array_delimited_by_null": True, "type": "^t"}},
    )
    r(b"NSValue", b"pointValue", {"retval": {"type": "{CGPoint=dd}"}})
    r(
        b"NSValue",
        b"pointerValue",
        {"retval": {"type": "^v"}, "suggestion": "use something else"},
    )
    r(b"NSValue", b"rangeValue", {"retval": {"type": "{_NSRange=QQ}"}})
    r(
        b"NSValue",
        b"rectValue",
        {"retval": {"type": "{CGRect={CGPoint=dd}{CGSize=dd}}"}},
    )
    r(b"NSValue", b"sizeValue", {"retval": {"type": "{CGSize=dd}"}})
    r(
        b"NSValue",
        b"value:withObjCType:",
        {
            "arguments": {
                2: {
                    "type": "^v",
                    "type_modifier": b"n",
                    "c_array_of_variable_length": True,
                },
                3: {
                    "c_array_delimited_by_null": True,
                    "type": "^t",
                    "type_modifier": b"n",
                },
            },
            "suggestion": "use something else",
        },
    )
    r(
        b"NSValue",
        b"valueWithBytes:objCType:",
        {
            "arguments": {
                2: {
                    "type": "^v",
                    "type_modifier": b"n",
                    "c_array_of_variable_length": True,
                },
                3: {
                    "c_array_delimited_by_null": True,
                    "type": "^t",
                    "type_modifier": b"n",
                },
            },
            "suggestion": "use something else",
        },
    )
    r(b"NSValue", b"valueWithPoint:", {"arguments": {2: {"type": "{CGPoint=dd}"}}})
    r(
        b"NSValue",
        b"valueWithPointer:",
        {"arguments": {2: {"type": "^v"}}, "suggestion": "use some other method"},
    )
    r(b"NSValue", b"valueWithRange:", {"arguments": {2: {"type": "{_NSRange=QQ}"}}})
    r(
        b"NSValue",
        b"valueWithRect:",
        {"arguments": {2: {"type": "{CGRect={CGPoint=dd}{CGSize=dd}}"}}},
    )
    r(b"NSValue", b"valueWithSize:", {"arguments": {2: {"type": "{CGSize=dd}"}}})
    r(b"NSValueTransformer", b"allowsReverseTransformation", {"retval": {"type": "Z"}})
    r(
        b"NSXMLDTD",
        b"initWithContentsOfURL:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSXMLDTD",
        b"initWithData:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"NSXMLDTDNode", b"isExternal", {"retval": {"type": "Z"}})
    r(
        b"NSXMLDocument",
        b"initWithContentsOfURL:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSXMLDocument",
        b"initWithData:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSXMLDocument",
        b"initWithXMLString:options:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"NSXMLDocument", b"isStandalone", {"retval": {"type": "Z"}})
    r(
        b"NSXMLDocument",
        b"objectByApplyingXSLT:arguments:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSXMLDocument",
        b"objectByApplyingXSLTAtURL:arguments:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSXMLDocument",
        b"objectByApplyingXSLTString:arguments:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(b"NSXMLDocument", b"setStandalone:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"NSXMLDocument",
        b"validateAndReturnError:",
        {"retval": {"type": "Z"}, "arguments": {2: {"type_modifier": b"o"}}},
    )
    r(
        b"NSXMLElement",
        b"initWithXMLString:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSXMLElement",
        b"normalizeAdjacentTextNodesPreservingCDATA:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSXMLNode",
        b"canonicalXMLStringPreservingComments:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSXMLNode",
        b"nodesForXPath:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSXMLNode",
        b"objectsForXQuery:constants:error:",
        {"arguments": {4: {"type_modifier": b"o"}}},
    )
    r(
        b"NSXMLNode",
        b"objectsForXQuery:error:",
        {"arguments": {3: {"type_modifier": b"o"}}},
    )
    r(
        b"NSXMLNode",
        b"setStringValue:resolvingEntities:",
        {"arguments": {3: {"type": "Z"}}},
    )
    r(b"NSXMLParser", b"parse", {"retval": {"type": "Z"}})
    r(b"NSXMLParser", b"setShouldProcessNamespaces:", {"arguments": {2: {"type": "Z"}}})
    r(
        b"NSXMLParser",
        b"setShouldReportNamespacePrefixes:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(
        b"NSXMLParser",
        b"setShouldResolveExternalEntities:",
        {"arguments": {2: {"type": "Z"}}},
    )
    r(b"NSXMLParser", b"shouldProcessNamespaces", {"retval": {"type": "Z"}})
    r(b"NSXMLParser", b"shouldReportNamespacePrefixes", {"retval": {"type": "Z"}})
    r(b"NSXMLParser", b"shouldResolveExternalEntities", {"retval": {"type": "Z"}})
    r(
        b"NSXPCConnection",
        b"interruptionHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}},
                }
            }
        },
    )
    r(
        b"NSXPCConnection",
        b"invalidationHandler",
        {
            "retval": {
                "callable": {
                    "retval": {"type": b"v"},
                    "arguments": {0: {"type": b"^v"}},
                }
            }
        },
    )
    r(
        b"NSXPCConnection",
        b"remoteObjectProxyWithErrorHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSXPCConnection",
        b"scheduleSendBarrierBlock:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSXPCConnection",
        b"setInterruptionHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSXPCConnection",
        b"setInvalidationHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}},
                    }
                }
            }
        },
    )
    r(
        b"NSXPCConnection",
        b"synchronousRemoteObjectProxyWithErrorHandler:",
        {
            "arguments": {
                2: {
                    "callable": {
                        "retval": {"type": b"v"},
                        "arguments": {0: {"type": b"^v"}, 1: {"type": b"@"}},
                    }
                }
            }
        },
    )
    r(
        b"NSXPCInterface",
        b"XPCTypeForSelector:argumentIndex:ofReply:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"NSXPCInterface",
        b"classesForSelector:argumentIndex:ofReply:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"NSXPCInterface",
        b"interfaceForSelector:argumentIndex:ofReply:",
        {"arguments": {4: {"type": b"Z"}}},
    )
    r(
        b"NSXPCInterface",
        b"setClasses:forSelector:argumentIndex:ofReply:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(
        b"NSXPCInterface",
        b"setInterface:forSelector:argumentIndex:ofReply:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(
        b"NSXPCInterface",
        b"setXPCType:forSelector:argumentIndex:ofReply:",
        {"arguments": {5: {"type": b"Z"}}},
    )
    r(
        b"null",
        b"differenceFromArray:withOptions:usingEquivalenceTest:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"null",
        b"differenceFromOrderedSet:withOptions:usingEquivalenceTest:",
        {
            "arguments": {
                4: {
                    "callable": {
                        "retval": {"type": b"Z"},
                        "arguments": {
                            0: {"type": b"^v"},
                            1: {"type": b"@"},
                            2: {"type": b"@"},
                        },
                    }
                }
            }
        },
    )
    r(
        b"null",
        b"setCustomPronoun:forLanguage:error:",
        {"retval": {"type": b"Z"}, "arguments": {4: {"type_modifier": b"o"}}},
    )
finally:
    objc._updatingMetadata(False)

objc.registerNewKeywordsFromSelector("NSAffineTransform", b"initWithTransform:")
objc.registerNewKeywordsFromSelector("NSAppleEventDescriptor", b"initWithAEDescNoCopy:")
objc.registerNewKeywordsFromSelector(
    "NSAppleEventDescriptor", b"initWithDescriptorType:bytes:length:"
)
objc.registerNewKeywordsFromSelector(
    "NSAppleEventDescriptor", b"initWithDescriptorType:data:"
)
objc.registerNewKeywordsFromSelector(
    "NSAppleEventDescriptor",
    b"initWithEventClass:eventID:targetDescriptor:returnID:transactionID:",
)
objc.registerNewKeywordsFromSelector("NSAppleScript", b"initWithContentsOfURL:error:")
objc.registerNewKeywordsFromSelector("NSAppleScript", b"initWithSource:")
objc.registerNewKeywordsFromSelector("NSArchiver", b"initForWritingWithMutableData:")
objc.registerNewKeywordsFromSelector("NSArray", b"initWithArray:")
objc.registerNewKeywordsFromSelector("NSArray", b"initWithArray:copyItems:")
objc.registerNewKeywordsFromSelector("NSArray", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSArray", b"initWithContentsOfFile:")
objc.registerNewKeywordsFromSelector("NSArray", b"initWithContentsOfURL:")
objc.registerNewKeywordsFromSelector("NSArray", b"initWithContentsOfURL:error:")
objc.registerNewKeywordsFromSelector("NSArray", b"initWithObjects:")
objc.registerNewKeywordsFromSelector("NSArray", b"initWithObjects:count:")
objc.registerNewKeywordsFromSelector("NSAttributedString", b"initWithAttributedString:")
objc.registerNewKeywordsFromSelector(
    "NSAttributedString", b"initWithContentsOfMarkdownFileAtURL:options:baseURL:error:"
)
objc.registerNewKeywordsFromSelector(
    "NSAttributedString", b"initWithFormat:options:locale:"
)
objc.registerNewKeywordsFromSelector(
    "NSAttributedString", b"initWithFormat:options:locale:arguments:"
)
objc.registerNewKeywordsFromSelector(
    "NSAttributedString", b"initWithFormat:options:locale:context:"
)
objc.registerNewKeywordsFromSelector(
    "NSAttributedString", b"initWithFormat:options:locale:context:arguments:"
)
objc.registerNewKeywordsFromSelector(
    "NSAttributedString", b"initWithMarkdown:options:baseURL:error:"
)
objc.registerNewKeywordsFromSelector(
    "NSAttributedString", b"initWithMarkdownString:options:baseURL:error:"
)
objc.registerNewKeywordsFromSelector("NSAttributedString", b"initWithString:")
objc.registerNewKeywordsFromSelector(
    "NSAttributedString", b"initWithString:attributes:"
)
objc.registerNewKeywordsFromSelector(
    "NSAttributedStringMarkdownSourcePosition",
    b"initWithStartLine:startColumn:endLine:endColumn:",
)
objc.registerNewKeywordsFromSelector(
    "NSBackgroundActivityScheduler", b"initWithIdentifier:"
)
objc.registerNewKeywordsFromSelector("NSBundle", b"initWithPath:")
objc.registerNewKeywordsFromSelector("NSBundle", b"initWithURL:")
objc.registerNewKeywordsFromSelector("NSBundleResourceRequest", b"initWithTags:")
objc.registerNewKeywordsFromSelector("NSBundleResourceRequest", b"initWithTags:bundle:")
objc.registerNewKeywordsFromSelector("NSCachedURLResponse", b"initWithResponse:data:")
objc.registerNewKeywordsFromSelector(
    "NSCachedURLResponse", b"initWithResponse:data:userInfo:storagePolicy:"
)
objc.registerNewKeywordsFromSelector("NSCalendar", b"initWithCalendarIdentifier:")
objc.registerNewKeywordsFromSelector("NSCalendarDate", b"initWithString:")
objc.registerNewKeywordsFromSelector(
    "NSCalendarDate", b"initWithString:calendarFormat:"
)
objc.registerNewKeywordsFromSelector(
    "NSCalendarDate", b"initWithString:calendarFormat:locale:"
)
objc.registerNewKeywordsFromSelector(
    "NSCalendarDate", b"initWithYear:month:day:hour:minute:second:timeZone:"
)
objc.registerNewKeywordsFromSelector("NSCharacterSet", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSComparisonPredicate", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "NSComparisonPredicate", b"initWithLeftExpression:rightExpression:customSelector:"
)
objc.registerNewKeywordsFromSelector(
    "NSComparisonPredicate",
    b"initWithLeftExpression:rightExpression:modifier:type:options:",
)
objc.registerNewKeywordsFromSelector("NSCompoundPredicate", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "NSCompoundPredicate", b"initWithType:subpredicates:"
)
objc.registerNewKeywordsFromSelector("NSConditionLock", b"initWithCondition:")
objc.registerNewKeywordsFromSelector("NSConnection", b"initWithReceivePort:sendPort:")
objc.registerNewKeywordsFromSelector("NSCountedSet", b"initWithArray:")
objc.registerNewKeywordsFromSelector("NSCountedSet", b"initWithCapacity:")
objc.registerNewKeywordsFromSelector("NSCountedSet", b"initWithSet:")
objc.registerNewKeywordsFromSelector("NSData", b"initWithBase64EncodedData:options:")
objc.registerNewKeywordsFromSelector("NSData", b"initWithBase64EncodedString:options:")
objc.registerNewKeywordsFromSelector("NSData", b"initWithBase64Encoding:")
objc.registerNewKeywordsFromSelector("NSData", b"initWithBytes:length:")
objc.registerNewKeywordsFromSelector("NSData", b"initWithBytesNoCopy:length:")
objc.registerNewKeywordsFromSelector(
    "NSData", b"initWithBytesNoCopy:length:deallocator:"
)
objc.registerNewKeywordsFromSelector(
    "NSData", b"initWithBytesNoCopy:length:freeWhenDone:"
)
objc.registerNewKeywordsFromSelector("NSData", b"initWithContentsOfFile:")
objc.registerNewKeywordsFromSelector("NSData", b"initWithContentsOfFile:options:error:")
objc.registerNewKeywordsFromSelector("NSData", b"initWithContentsOfMappedFile:")
objc.registerNewKeywordsFromSelector("NSData", b"initWithContentsOfURL:")
objc.registerNewKeywordsFromSelector("NSData", b"initWithContentsOfURL:options:error:")
objc.registerNewKeywordsFromSelector("NSData", b"initWithData:")
objc.registerNewKeywordsFromSelector("NSDataDetector", b"initWithTypes:error:")
objc.registerNewKeywordsFromSelector("NSDate", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSDate", b"initWithString:")
objc.registerNewKeywordsFromSelector("NSDate", b"initWithTimeInterval:sinceDate:")
objc.registerNewKeywordsFromSelector("NSDate", b"initWithTimeIntervalSince1970:")
objc.registerNewKeywordsFromSelector("NSDate", b"initWithTimeIntervalSinceNow:")
objc.registerNewKeywordsFromSelector(
    "NSDate", b"initWithTimeIntervalSinceReferenceDate:"
)
objc.registerNewKeywordsFromSelector(
    "NSDateFormatter", b"initWithDateFormat:allowNaturalLanguage:"
)
objc.registerNewKeywordsFromSelector("NSDateInterval", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSDateInterval", b"initWithStartDate:duration:")
objc.registerNewKeywordsFromSelector("NSDateInterval", b"initWithStartDate:endDate:")
objc.registerNewKeywordsFromSelector("NSDecimalNumber", b"initWithDecimal:")
objc.registerNewKeywordsFromSelector(
    "NSDecimalNumber", b"initWithMantissa:exponent:isNegative:"
)
objc.registerNewKeywordsFromSelector("NSDecimalNumber", b"initWithString:")
objc.registerNewKeywordsFromSelector("NSDecimalNumber", b"initWithString:locale:")
objc.registerNewKeywordsFromSelector(
    "NSDecimalNumberHandler",
    b"initWithRoundingMode:scale:raiseOnExactness:raiseOnOverflow:raiseOnUnderflow:raiseOnDivideByZero:",
)
objc.registerNewKeywordsFromSelector("NSDictionary", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSDictionary", b"initWithContentsOfFile:")
objc.registerNewKeywordsFromSelector("NSDictionary", b"initWithContentsOfURL:")
objc.registerNewKeywordsFromSelector("NSDictionary", b"initWithContentsOfURL:error:")
objc.registerNewKeywordsFromSelector("NSDictionary", b"initWithDictionary:")
objc.registerNewKeywordsFromSelector("NSDictionary", b"initWithDictionary:copyItems:")
objc.registerNewKeywordsFromSelector("NSDictionary", b"initWithObjects:forKeys:")
objc.registerNewKeywordsFromSelector("NSDictionary", b"initWithObjects:forKeys:count:")
objc.registerNewKeywordsFromSelector("NSDictionary", b"initWithObjectsAndKeys:")
objc.registerNewKeywordsFromSelector("NSDimension", b"initWithSymbol:converter:")
objc.registerNewKeywordsFromSelector("NSDistantObject", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSDistantObject", b"initWithLocal:connection:")
objc.registerNewKeywordsFromSelector("NSDistantObject", b"initWithTarget:connection:")
objc.registerNewKeywordsFromSelector("NSDistributedLock", b"initWithPath:")
objc.registerNewKeywordsFromSelector("NSError", b"initWithDomain:code:userInfo:")
objc.registerNewKeywordsFromSelector("NSException", b"initWithName:reason:userInfo:")
objc.registerNewKeywordsFromSelector("NSExpression", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSExpression", b"initWithExpressionType:")
objc.registerNewKeywordsFromSelector("NSFileCoordinator", b"initWithFilePresenter:")
objc.registerNewKeywordsFromSelector("NSFileHandle", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSFileHandle", b"initWithFileDescriptor:")
objc.registerNewKeywordsFromSelector(
    "NSFileHandle", b"initWithFileDescriptor:closeOnDealloc:"
)
objc.registerNewKeywordsFromSelector("NSFileSecurity", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSFileWrapper", b"initDirectoryWithFileWrappers:")
objc.registerNewKeywordsFromSelector("NSFileWrapper", b"initRegularFileWithContents:")
objc.registerNewKeywordsFromSelector(
    "NSFileWrapper", b"initSymbolicLinkWithDestination:"
)
objc.registerNewKeywordsFromSelector(
    "NSFileWrapper", b"initSymbolicLinkWithDestinationURL:"
)
objc.registerNewKeywordsFromSelector("NSFileWrapper", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSFileWrapper", b"initWithPath:")
objc.registerNewKeywordsFromSelector(
    "NSFileWrapper", b"initWithSerializedRepresentation:"
)
objc.registerNewKeywordsFromSelector("NSFileWrapper", b"initWithURL:options:error:")
objc.registerNewKeywordsFromSelector("NSHTTPCookie", b"initWithProperties:")
objc.registerNewKeywordsFromSelector(
    "NSHTTPURLResponse", b"initWithURL:statusCode:HTTPVersion:headerFields:"
)
objc.registerNewKeywordsFromSelector("NSHashTable", b"initWithOptions:capacity:")
objc.registerNewKeywordsFromSelector(
    "NSHashTable", b"initWithPointerFunctions:capacity:"
)
objc.registerNewKeywordsFromSelector("NSIndexPath", b"initWithIndex:")
objc.registerNewKeywordsFromSelector("NSIndexPath", b"initWithIndexes:length:")
objc.registerNewKeywordsFromSelector("NSIndexSet", b"initWithIndex:")
objc.registerNewKeywordsFromSelector("NSIndexSet", b"initWithIndexSet:")
objc.registerNewKeywordsFromSelector("NSIndexSet", b"initWithIndexesInRange:")
objc.registerNewKeywordsFromSelector(
    "NSIndexSpecifier",
    b"initWithContainerClassDescription:containerSpecifier:key:index:",
)
objc.registerNewKeywordsFromSelector("NSInflectionRuleExplicit", b"initWithMorphology:")
objc.registerNewKeywordsFromSelector("NSInputStream", b"initWithData:")
objc.registerNewKeywordsFromSelector("NSInputStream", b"initWithFileAtPath:")
objc.registerNewKeywordsFromSelector("NSInputStream", b"initWithURL:")
objc.registerNewKeywordsFromSelector("NSInvocationOperation", b"initWithInvocation:")
objc.registerNewKeywordsFromSelector(
    "NSInvocationOperation", b"initWithTarget:selector:object:"
)
objc.registerNewKeywordsFromSelector("NSItemProvider", b"initWithContentsOfURL:")
objc.registerNewKeywordsFromSelector("NSItemProvider", b"initWithItem:typeIdentifier:")
objc.registerNewKeywordsFromSelector("NSItemProvider", b"initWithObject:")
objc.registerNewKeywordsFromSelector(
    "NSKeyValueSharedObservers", b"initWithObservableClass:"
)
objc.registerNewKeywordsFromSelector(
    "NSKeyedArchiver", b"initForWritingWithMutableData:"
)
objc.registerNewKeywordsFromSelector("NSKeyedArchiver", b"initRequiringSecureCoding:")
objc.registerNewKeywordsFromSelector(
    "NSKeyedUnarchiver", b"initForReadingFromData:error:"
)
objc.registerNewKeywordsFromSelector("NSKeyedUnarchiver", b"initForReadingWithData:")
objc.registerNewKeywordsFromSelector(
    "NSLinguisticTagger", b"initWithTagSchemes:options:"
)
objc.registerNewKeywordsFromSelector("NSLocale", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSLocale", b"initWithLocaleIdentifier:")
objc.registerNewKeywordsFromSelector("NSLogicalTest", b"initAndTestWithTests:")
objc.registerNewKeywordsFromSelector("NSLogicalTest", b"initNotTestWithTest:")
objc.registerNewKeywordsFromSelector("NSLogicalTest", b"initOrTestWithTests:")
objc.registerNewKeywordsFromSelector("NSMachPort", b"initWithMachPort:")
objc.registerNewKeywordsFromSelector("NSMachPort", b"initWithMachPort:options:")
objc.registerNewKeywordsFromSelector(
    "NSMapTable", b"initWithKeyOptions:valueOptions:capacity:"
)
objc.registerNewKeywordsFromSelector(
    "NSMapTable", b"initWithKeyPointerFunctions:valuePointerFunctions:capacity:"
)
objc.registerNewKeywordsFromSelector("NSMeasurement", b"initWithDoubleValue:unit:")
objc.registerNewKeywordsFromSelector("NSMetadataItem", b"initWithURL:")
objc.registerNewKeywordsFromSelector(
    "NSMorphologyPronoun", b"initWithPronoun:morphology:dependentMorphology:"
)
objc.registerNewKeywordsFromSelector("NSMutableArray", b"initWithCapacity:")
objc.registerNewKeywordsFromSelector("NSMutableArray", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSMutableArray", b"initWithContentsOfFile:")
objc.registerNewKeywordsFromSelector("NSMutableArray", b"initWithContentsOfURL:")
objc.registerNewKeywordsFromSelector("NSMutableData", b"initWithCapacity:")
objc.registerNewKeywordsFromSelector("NSMutableData", b"initWithLength:")
objc.registerNewKeywordsFromSelector("NSMutableDictionary", b"initWithCapacity:")
objc.registerNewKeywordsFromSelector("NSMutableDictionary", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSMutableDictionary", b"initWithContentsOfFile:")
objc.registerNewKeywordsFromSelector("NSMutableDictionary", b"initWithContentsOfURL:")
objc.registerNewKeywordsFromSelector("NSMutableOrderedSet", b"initWithCapacity:")
objc.registerNewKeywordsFromSelector("NSMutableOrderedSet", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSMutableSet", b"initWithCapacity:")
objc.registerNewKeywordsFromSelector("NSMutableSet", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSMutableString", b"initWithCapacity:")
objc.registerNewKeywordsFromSelector("NSNameSpecifier", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "NSNameSpecifier", b"initWithContainerClassDescription:containerSpecifier:key:name:"
)
objc.registerNewKeywordsFromSelector("NSNetService", b"initWithDomain:type:name:")
objc.registerNewKeywordsFromSelector("NSNetService", b"initWithDomain:type:name:port:")
objc.registerNewKeywordsFromSelector("NSNotification", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSNotification", b"initWithName:object:userInfo:")
objc.registerNewKeywordsFromSelector(
    "NSNotificationQueue", b"initWithNotificationCenter:"
)
objc.registerNewKeywordsFromSelector("NSNumber", b"initWithBool:")
objc.registerNewKeywordsFromSelector("NSNumber", b"initWithChar:")
objc.registerNewKeywordsFromSelector("NSNumber", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSNumber", b"initWithDouble:")
objc.registerNewKeywordsFromSelector("NSNumber", b"initWithFloat:")
objc.registerNewKeywordsFromSelector("NSNumber", b"initWithInt:")
objc.registerNewKeywordsFromSelector("NSNumber", b"initWithInteger:")
objc.registerNewKeywordsFromSelector("NSNumber", b"initWithLong:")
objc.registerNewKeywordsFromSelector("NSNumber", b"initWithLongLong:")
objc.registerNewKeywordsFromSelector("NSNumber", b"initWithShort:")
objc.registerNewKeywordsFromSelector("NSNumber", b"initWithUnsignedChar:")
objc.registerNewKeywordsFromSelector("NSNumber", b"initWithUnsignedInt:")
objc.registerNewKeywordsFromSelector("NSNumber", b"initWithUnsignedInteger:")
objc.registerNewKeywordsFromSelector("NSNumber", b"initWithUnsignedLong:")
objc.registerNewKeywordsFromSelector("NSNumber", b"initWithUnsignedLongLong:")
objc.registerNewKeywordsFromSelector("NSNumber", b"initWithUnsignedShort:")
objc.registerNewKeywordsFromSelector(
    "NSOrderedCollectionChange", b"initWithObject:type:index:"
)
objc.registerNewKeywordsFromSelector(
    "NSOrderedCollectionChange", b"initWithObject:type:index:associatedIndex:"
)
objc.registerNewKeywordsFromSelector(
    "NSOrderedCollectionDifference", b"initWithChanges:"
)
objc.registerNewKeywordsFromSelector(
    "NSOrderedCollectionDifference",
    b"initWithInsertIndexes:insertedObjects:removeIndexes:removedObjects:",
)
objc.registerNewKeywordsFromSelector(
    "NSOrderedCollectionDifference",
    b"initWithInsertIndexes:insertedObjects:removeIndexes:removedObjects:additionalChanges:",
)
objc.registerNewKeywordsFromSelector("NSOrderedSet", b"initWithArray:")
objc.registerNewKeywordsFromSelector("NSOrderedSet", b"initWithArray:copyItems:")
objc.registerNewKeywordsFromSelector("NSOrderedSet", b"initWithArray:range:copyItems:")
objc.registerNewKeywordsFromSelector("NSOrderedSet", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSOrderedSet", b"initWithObject:")
objc.registerNewKeywordsFromSelector("NSOrderedSet", b"initWithObjects:")
objc.registerNewKeywordsFromSelector("NSOrderedSet", b"initWithObjects:count:")
objc.registerNewKeywordsFromSelector("NSOrderedSet", b"initWithOrderedSet:")
objc.registerNewKeywordsFromSelector("NSOrderedSet", b"initWithOrderedSet:copyItems:")
objc.registerNewKeywordsFromSelector(
    "NSOrderedSet", b"initWithOrderedSet:range:copyItems:"
)
objc.registerNewKeywordsFromSelector("NSOrderedSet", b"initWithSet:")
objc.registerNewKeywordsFromSelector("NSOrderedSet", b"initWithSet:copyItems:")
objc.registerNewKeywordsFromSelector("NSOrthography", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "NSOrthography", b"initWithDominantScript:languageMap:"
)
objc.registerNewKeywordsFromSelector("NSOutputStream", b"initToBuffer:capacity:")
objc.registerNewKeywordsFromSelector("NSOutputStream", b"initToFileAtPath:append:")
objc.registerNewKeywordsFromSelector("NSOutputStream", b"initWithURL:append:")
objc.registerNewKeywordsFromSelector("NSPointerArray", b"initWithOptions:")
objc.registerNewKeywordsFromSelector("NSPointerArray", b"initWithPointerFunctions:")
objc.registerNewKeywordsFromSelector("NSPointerFunctions", b"initWithOptions:")
objc.registerNewKeywordsFromSelector(
    "NSPortCoder", b"initWithReceivePort:sendPort:components:"
)
objc.registerNewKeywordsFromSelector(
    "NSPortMessage", b"initWithSendPort:receivePort:components:"
)
objc.registerNewKeywordsFromSelector(
    "NSPositionalSpecifier", b"initWithPosition:objectSpecifier:"
)
objc.registerNewKeywordsFromSelector("NSProgress", b"initWithParent:userInfo:")
objc.registerNewKeywordsFromSelector("NSProtocolChecker", b"initWithTarget:protocol:")
objc.registerNewKeywordsFromSelector("NSRangeSpecifier", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "NSRangeSpecifier",
    b"initWithContainerClassDescription:containerSpecifier:key:startSpecifier:endSpecifier:",
)
objc.registerNewKeywordsFromSelector(
    "NSRegularExpression", b"initWithPattern:options:error:"
)
objc.registerNewKeywordsFromSelector("NSRelativeSpecifier", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "NSRelativeSpecifier",
    b"initWithContainerClassDescription:containerSpecifier:key:relativePosition:baseSpecifier:",
)
objc.registerNewKeywordsFromSelector("NSScanner", b"initWithString:")
objc.registerNewKeywordsFromSelector(
    "NSScriptClassDescription", b"initWithSuiteName:className:dictionary:"
)
objc.registerNewKeywordsFromSelector("NSScriptCommand", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSScriptCommand", b"initWithCommandDescription:")
objc.registerNewKeywordsFromSelector("NSScriptCommandDescription", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "NSScriptCommandDescription", b"initWithSuiteName:commandName:dictionary:"
)
objc.registerNewKeywordsFromSelector("NSScriptObjectSpecifier", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "NSScriptObjectSpecifier",
    b"initWithContainerClassDescription:containerSpecifier:key:",
)
objc.registerNewKeywordsFromSelector(
    "NSScriptObjectSpecifier", b"initWithContainerSpecifier:key:"
)
objc.registerNewKeywordsFromSelector("NSScriptWhoseTest", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSSet", b"initWithArray:")
objc.registerNewKeywordsFromSelector("NSSet", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSSet", b"initWithObjects:")
objc.registerNewKeywordsFromSelector("NSSet", b"initWithObjects:count:")
objc.registerNewKeywordsFromSelector("NSSet", b"initWithSet:")
objc.registerNewKeywordsFromSelector("NSSet", b"initWithSet:copyItems:")
objc.registerNewKeywordsFromSelector(
    "NSSocketPort", b"initRemoteWithProtocolFamily:socketType:protocol:address:"
)
objc.registerNewKeywordsFromSelector("NSSocketPort", b"initRemoteWithTCPPort:host:")
objc.registerNewKeywordsFromSelector(
    "NSSocketPort", b"initWithProtocolFamily:socketType:protocol:address:"
)
objc.registerNewKeywordsFromSelector(
    "NSSocketPort", b"initWithProtocolFamily:socketType:protocol:socket:"
)
objc.registerNewKeywordsFromSelector("NSSocketPort", b"initWithTCPPort:")
objc.registerNewKeywordsFromSelector("NSSortDescriptor", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSSortDescriptor", b"initWithKey:ascending:")
objc.registerNewKeywordsFromSelector(
    "NSSortDescriptor", b"initWithKey:ascending:comparator:"
)
objc.registerNewKeywordsFromSelector(
    "NSSortDescriptor", b"initWithKey:ascending:selector:"
)
objc.registerNewKeywordsFromSelector("NSSpecifierTest", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "NSSpecifierTest", b"initWithObjectSpecifier:comparisonOperator:testObject:"
)
objc.registerNewKeywordsFromSelector("NSString", b"initWithBytes:length:encoding:")
objc.registerNewKeywordsFromSelector(
    "NSString", b"initWithBytesNoCopy:length:encoding:deallocator:"
)
objc.registerNewKeywordsFromSelector(
    "NSString", b"initWithBytesNoCopy:length:encoding:freeWhenDone:"
)
objc.registerNewKeywordsFromSelector("NSString", b"initWithCString:")
objc.registerNewKeywordsFromSelector("NSString", b"initWithCString:encoding:")
objc.registerNewKeywordsFromSelector("NSString", b"initWithCString:length:")
objc.registerNewKeywordsFromSelector(
    "NSString", b"initWithCStringNoCopy:length:freeWhenDone:"
)
objc.registerNewKeywordsFromSelector("NSString", b"initWithCharacters:length:")
objc.registerNewKeywordsFromSelector(
    "NSString", b"initWithCharactersNoCopy:length:deallocator:"
)
objc.registerNewKeywordsFromSelector(
    "NSString", b"initWithCharactersNoCopy:length:freeWhenDone:"
)
objc.registerNewKeywordsFromSelector("NSString", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSString", b"initWithContentsOfFile:")
objc.registerNewKeywordsFromSelector(
    "NSString", b"initWithContentsOfFile:encoding:error:"
)
objc.registerNewKeywordsFromSelector(
    "NSString", b"initWithContentsOfFile:usedEncoding:error:"
)
objc.registerNewKeywordsFromSelector("NSString", b"initWithContentsOfURL:")
objc.registerNewKeywordsFromSelector(
    "NSString", b"initWithContentsOfURL:encoding:error:"
)
objc.registerNewKeywordsFromSelector(
    "NSString", b"initWithContentsOfURL:usedEncoding:error:"
)
objc.registerNewKeywordsFromSelector("NSString", b"initWithData:encoding:")
objc.registerNewKeywordsFromSelector("NSString", b"initWithFormat:")
objc.registerNewKeywordsFromSelector("NSString", b"initWithFormat:arguments:")
objc.registerNewKeywordsFromSelector("NSString", b"initWithFormat:locale:")
objc.registerNewKeywordsFromSelector("NSString", b"initWithFormat:locale:arguments:")
objc.registerNewKeywordsFromSelector("NSString", b"initWithString:")
objc.registerNewKeywordsFromSelector("NSString", b"initWithUTF8String:")
objc.registerNewKeywordsFromSelector(
    "NSString", b"initWithValidatedFormat:validFormatSpecifiers:arguments:error:"
)
objc.registerNewKeywordsFromSelector(
    "NSString", b"initWithValidatedFormat:validFormatSpecifiers:error:"
)
objc.registerNewKeywordsFromSelector(
    "NSString", b"initWithValidatedFormat:validFormatSpecifiers:locale:arguments:error:"
)
objc.registerNewKeywordsFromSelector(
    "NSString", b"initWithValidatedFormat:validFormatSpecifiers:locale:error:"
)
objc.registerNewKeywordsFromSelector("NSThread", b"initWithBlock:")
objc.registerNewKeywordsFromSelector("NSThread", b"initWithTarget:selector:object:")
objc.registerNewKeywordsFromSelector("NSTimeZone", b"initWithName:")
objc.registerNewKeywordsFromSelector("NSTimeZone", b"initWithName:data:")
objc.registerNewKeywordsFromSelector(
    "NSTimer", b"initWithFireDate:interval:repeats:block:"
)
objc.registerNewKeywordsFromSelector(
    "NSTimer", b"initWithFireDate:interval:target:selector:userInfo:repeats:"
)
objc.registerNewKeywordsFromSelector(
    "NSURL", b"initAbsoluteURLWithDataRepresentation:relativeToURL:"
)
objc.registerNewKeywordsFromSelector(
    "NSURL",
    b"initByResolvingBookmarkData:options:relativeToURL:bookmarkDataIsStale:error:",
)
objc.registerNewKeywordsFromSelector(
    "NSURL", b"initFileURLWithFileSystemRepresentation:isDirectory:relativeToURL:"
)
objc.registerNewKeywordsFromSelector("NSURL", b"initFileURLWithPath:")
objc.registerNewKeywordsFromSelector("NSURL", b"initFileURLWithPath:isDirectory:")
objc.registerNewKeywordsFromSelector(
    "NSURL", b"initFileURLWithPath:isDirectory:relativeToURL:"
)
objc.registerNewKeywordsFromSelector("NSURL", b"initFileURLWithPath:relativeToURL:")
objc.registerNewKeywordsFromSelector(
    "NSURL", b"initWithDataRepresentation:relativeToURL:"
)
objc.registerNewKeywordsFromSelector("NSURL", b"initWithScheme:host:path:")
objc.registerNewKeywordsFromSelector("NSURL", b"initWithString:")
objc.registerNewKeywordsFromSelector(
    "NSURL", b"initWithString:encodingInvalidCharacters:"
)
objc.registerNewKeywordsFromSelector("NSURL", b"initWithString:relativeToURL:")
objc.registerNewKeywordsFromSelector(
    "NSURLAuthenticationChallenge", b"initWithAuthenticationChallenge:sender:"
)
objc.registerNewKeywordsFromSelector(
    "NSURLAuthenticationChallenge",
    b"initWithProtectionSpace:proposedCredential:previousFailureCount:failureResponse:error:sender:",
)
objc.registerNewKeywordsFromSelector(
    "NSURLCache", b"initWithMemoryCapacity:diskCapacity:directoryURL:"
)
objc.registerNewKeywordsFromSelector(
    "NSURLCache", b"initWithMemoryCapacity:diskCapacity:diskPath:"
)
objc.registerNewKeywordsFromSelector("NSURLComponents", b"initWithString:")
objc.registerNewKeywordsFromSelector(
    "NSURLComponents", b"initWithString:encodingInvalidCharacters:"
)
objc.registerNewKeywordsFromSelector(
    "NSURLComponents", b"initWithURL:resolvingAgainstBaseURL:"
)
objc.registerNewKeywordsFromSelector("NSURLConnection", b"initWithRequest:delegate:")
objc.registerNewKeywordsFromSelector(
    "NSURLConnection", b"initWithRequest:delegate:startImmediately:"
)
objc.registerNewKeywordsFromSelector(
    "NSURLCredential", b"initWithIdentity:certificates:persistence:"
)
objc.registerNewKeywordsFromSelector("NSURLCredential", b"initWithTrust:")
objc.registerNewKeywordsFromSelector(
    "NSURLCredential", b"initWithUser:password:persistence:"
)
objc.registerNewKeywordsFromSelector("NSURLDownload", b"initWithRequest:delegate:")
objc.registerNewKeywordsFromSelector(
    "NSURLDownload", b"initWithResumeData:delegate:path:"
)
objc.registerNewKeywordsFromSelector("NSURLHandle", b"initWithURL:cached:")
objc.registerNewKeywordsFromSelector(
    "NSURLProtectionSpace", b"initWithHost:port:protocol:realm:authenticationMethod:"
)
objc.registerNewKeywordsFromSelector(
    "NSURLProtectionSpace", b"initWithProxyHost:port:type:realm:authenticationMethod:"
)
objc.registerNewKeywordsFromSelector(
    "NSURLProtocol", b"initWithRequest:cachedResponse:client:"
)
objc.registerNewKeywordsFromSelector(
    "NSURLProtocol", b"initWithTask:cachedResponse:client:"
)
objc.registerNewKeywordsFromSelector("NSURLQueryItem", b"initWithName:value:")
objc.registerNewKeywordsFromSelector("NSURLRequest", b"initWithURL:")
objc.registerNewKeywordsFromSelector(
    "NSURLRequest", b"initWithURL:cachePolicy:timeoutInterval:"
)
objc.registerNewKeywordsFromSelector(
    "NSURLResponse", b"initWithURL:MIMEType:expectedContentLength:textEncodingName:"
)
objc.registerNewKeywordsFromSelector("NSURLSessionWebSocketMessage", b"initWithData:")
objc.registerNewKeywordsFromSelector("NSURLSessionWebSocketMessage", b"initWithString:")
objc.registerNewKeywordsFromSelector("NSUUID", b"initWithUUIDBytes:")
objc.registerNewKeywordsFromSelector("NSUUID", b"initWithUUIDString:")
objc.registerNewKeywordsFromSelector("NSUnarchiver", b"initForReadingWithData:")
objc.registerNewKeywordsFromSelector("NSUniqueIDSpecifier", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "NSUniqueIDSpecifier",
    b"initWithContainerClassDescription:containerSpecifier:key:uniqueID:",
)
objc.registerNewKeywordsFromSelector("NSUnit", b"initWithSymbol:")
objc.registerNewKeywordsFromSelector("NSUnitConverterLinear", b"initWithCoefficient:")
objc.registerNewKeywordsFromSelector(
    "NSUnitConverterLinear", b"initWithCoefficient:constant:"
)
objc.registerNewKeywordsFromSelector("NSUserActivity", b"initWithActivityType:")
objc.registerNewKeywordsFromSelector("NSUserDefaults", b"initWithSuiteName:")
objc.registerNewKeywordsFromSelector("NSUserDefaults", b"initWithUser:")
objc.registerNewKeywordsFromSelector("NSUserScriptTask", b"initWithURL:error:")
objc.registerNewKeywordsFromSelector("NSValue", b"initWithBytes:objCType:")
objc.registerNewKeywordsFromSelector("NSValue", b"initWithCoder:")
objc.registerNewKeywordsFromSelector("NSWhoseSpecifier", b"initWithCoder:")
objc.registerNewKeywordsFromSelector(
    "NSWhoseSpecifier",
    b"initWithContainerClassDescription:containerSpecifier:key:test:",
)
objc.registerNewKeywordsFromSelector(
    "NSXMLDTD", b"initWithContentsOfURL:options:error:"
)
objc.registerNewKeywordsFromSelector("NSXMLDTD", b"initWithData:options:error:")
objc.registerNewKeywordsFromSelector("NSXMLDTD", b"initWithKind:options:")
objc.registerNewKeywordsFromSelector("NSXMLDTDNode", b"initWithKind:options:")
objc.registerNewKeywordsFromSelector("NSXMLDTDNode", b"initWithXMLString:")
objc.registerNewKeywordsFromSelector(
    "NSXMLDocument", b"initWithContentsOfURL:options:error:"
)
objc.registerNewKeywordsFromSelector("NSXMLDocument", b"initWithData:options:error:")
objc.registerNewKeywordsFromSelector("NSXMLDocument", b"initWithRootElement:")
objc.registerNewKeywordsFromSelector(
    "NSXMLDocument", b"initWithXMLString:options:error:"
)
objc.registerNewKeywordsFromSelector("NSXMLElement", b"initWithKind:options:")
objc.registerNewKeywordsFromSelector("NSXMLElement", b"initWithName:")
objc.registerNewKeywordsFromSelector("NSXMLElement", b"initWithName:URI:")
objc.registerNewKeywordsFromSelector("NSXMLElement", b"initWithName:stringValue:")
objc.registerNewKeywordsFromSelector("NSXMLElement", b"initWithXMLString:error:")
objc.registerNewKeywordsFromSelector("NSXMLNode", b"initWithKind:")
objc.registerNewKeywordsFromSelector("NSXMLNode", b"initWithKind:options:")
objc.registerNewKeywordsFromSelector("NSXMLParser", b"initWithContentsOfURL:")
objc.registerNewKeywordsFromSelector("NSXMLParser", b"initWithData:")
objc.registerNewKeywordsFromSelector("NSXMLParser", b"initWithStream:")
objc.registerNewKeywordsFromSelector("NSXPCConnection", b"initWithListenerEndpoint:")
objc.registerNewKeywordsFromSelector(
    "NSXPCConnection", b"initWithMachServiceName:options:"
)
objc.registerNewKeywordsFromSelector("NSXPCConnection", b"initWithServiceName:")
objc.registerNewKeywordsFromSelector("NSXPCListener", b"initWithMachServiceName:")
protocols = {
    "NSKeyValueSharedObserverRegistration": objc.informal_protocol(
        "NSKeyValueSharedObserverRegistration",
        [objc.selector(None, b"setSharedObservers:", b"v@:@", isRequired=False)],
    ),
    "NSCoderMethods": objc.informal_protocol(
        "NSCoderMethods",
        [
            objc.selector(None, b"classForCoder", b"#@:", isRequired=False),
            objc.selector(None, b"version", b"q@:", isRequired=False),
            objc.selector(None, b"setVersion:", b"v@:q", isRequired=False),
            objc.selector(
                None, b"replacementObjectForCoder:", b"@@:@", isRequired=False
            ),
            objc.selector(None, b"awakeAfterUsingCoder:", b"@@:@", isRequired=False),
        ],
    ),
    "NSCopyLinkMoveHandler": objc.informal_protocol(
        "NSCopyLinkMoveHandler",
        [
            objc.selector(
                None,
                b"fileManager:shouldProceedAfterError:",
                b"Z@:@@",
                isRequired=False,
            ),
            objc.selector(
                None, b"fileManager:willProcessPath:", b"v@:@@", isRequired=False
            ),
        ],
    ),
    "NSScriptClassDescription": objc.informal_protocol(
        "NSScriptClassDescription",
        [
            objc.selector(None, b"className", b"@@:", isRequired=False),
            objc.selector(None, b"classCode", b"I@:", isRequired=False),
        ],
    ),
    "NSKeyValueObserverNotification": objc.informal_protocol(
        "NSKeyValueObserverNotification",
        [
            objc.selector(
                None, b"didChange:valuesAtIndexes:forKey:", b"v@:Q@@", isRequired=False
            ),
            objc.selector(None, b"didChangeValueForKey:", b"v@:@", isRequired=False),
            objc.selector(
                None, b"willChange:valuesAtIndexes:forKey:", b"v@:Q@@", isRequired=False
            ),
            objc.selector(None, b"willChangeValueForKey:", b"v@:@", isRequired=False),
            objc.selector(
                None,
                b"didChangeValueForKey:withSetMutation:usingObjects:",
                b"v@:@Q@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"willChangeValueForKey:withSetMutation:usingObjects:",
                b"v@:@Q@",
                isRequired=False,
            ),
        ],
    ),
    "NSKeyValueCoding": objc.informal_protocol(
        "NSKeyValueCoding",
        [
            objc.selector(
                None, b"mutableOrderedSetValueForKeyPath:", b"@@:@", isRequired=False
            ),
            objc.selector(None, b"mutableSetValueForKey:", b"@@:@", isRequired=False),
            objc.selector(
                None, b"validateValue:forKeyPath:error:", b"Z@:^@@^@", isRequired=False
            ),
            objc.selector(None, b"valueForKey:", b"@@:@", isRequired=False),
            objc.selector(None, b"mutableArrayValueForKey:", b"@@:@", isRequired=False),
            objc.selector(
                None, b"dictionaryWithValuesForKeys:", b"@@:@", isRequired=False
            ),
            objc.selector(None, b"setValue:forKey:", b"v@:@@", isRequired=False),
            objc.selector(
                None, b"mutableOrderedSetValueForKey:", b"@@:@", isRequired=False
            ),
            objc.selector(
                None, b"validateValue:forKey:error:", b"Z@:^@@^@", isRequired=False
            ),
            objc.selector(None, b"valueForKeyPath:", b"@@:@", isRequired=False),
            objc.selector(None, b"valueForUndefinedKey:", b"@@:@", isRequired=False),
            objc.selector(
                None, b"mutableArrayValueForKeyPath:", b"@@:@", isRequired=False
            ),
            objc.selector(None, b"setNilValueForKey:", b"v@:@", isRequired=False),
            objc.selector(
                None, b"accessInstanceVariablesDirectly", b"Z@:", isRequired=False
            ),
            objc.selector(None, b"setValue:forKeyPath:", b"v@:@@", isRequired=False),
            objc.selector(
                None, b"setValuesForKeysWithDictionary:", b"v@:@", isRequired=False
            ),
            objc.selector(
                None, b"setValue:forUndefinedKey:", b"v@:@@", isRequired=False
            ),
            objc.selector(
                None, b"mutableSetValueForKeyPath:", b"@@:@", isRequired=False
            ),
        ],
    ),
    "NSDeprecatedMethods": objc.informal_protocol(
        "NSDeprecatedMethods",
        [objc.selector(None, b"poseAsClass:", b"v@:#", isRequired=False)],
    ),
    "NSScriptKeyValueCoding": objc.informal_protocol(
        "NSScriptKeyValueCoding",
        [
            objc.selector(
                None,
                b"removeValueAtIndex:fromPropertyWithKey:",
                b"v@:Q@",
                isRequired=False,
            ),
            objc.selector(
                None, b"insertValue:inPropertyWithKey:", b"v@:@@", isRequired=False
            ),
            objc.selector(
                None,
                b"valueWithUniqueID:inPropertyWithKey:",
                b"@@:@@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"insertValue:atIndex:inPropertyWithKey:",
                b"v@:@Q@",
                isRequired=False,
            ),
            objc.selector(None, b"coerceValue:forKey:", b"@@:@@", isRequired=False),
            objc.selector(
                None,
                b"replaceValueAtIndex:inPropertyWithKey:withValue:",
                b"v@:Q@@",
                isRequired=False,
            ),
            objc.selector(
                None, b"valueAtIndex:inPropertyWithKey:", b"@@:Q@", isRequired=False
            ),
            objc.selector(
                None, b"valueWithName:inPropertyWithKey:", b"@@:@@", isRequired=False
            ),
        ],
    ),
    "NSDiscardableContentProxy": objc.informal_protocol(
        "NSDiscardableContentProxy",
        [objc.selector(None, b"autoContentAccessingProxy", b"@@:", isRequired=False)],
    ),
    "NSDeprecatedKeyValueObservingCustomization": objc.informal_protocol(
        "NSDeprecatedKeyValueObservingCustomization",
        [
            objc.selector(
                None,
                b"setKeys:triggerChangeNotificationsForDependentKey:",
                b"v@:@@",
                isRequired=False,
            )
        ],
    ),
    "NSComparisonMethods": objc.informal_protocol(
        "NSComparisonMethods",
        [
            objc.selector(None, b"isCaseInsensitiveLike:", b"Z@:@", isRequired=False),
            objc.selector(None, b"isLessThan:", b"Z@:@", isRequired=False),
            objc.selector(None, b"isGreaterThanOrEqualTo:", b"Z@:@", isRequired=False),
            objc.selector(None, b"isNotEqualTo:", b"Z@:@", isRequired=False),
            objc.selector(None, b"isGreaterThan:", b"Z@:@", isRequired=False),
            objc.selector(None, b"isLike:", b"Z@:@", isRequired=False),
            objc.selector(None, b"isEqualTo:", b"Z@:@", isRequired=False),
            objc.selector(None, b"doesContain:", b"Z@:@", isRequired=False),
            objc.selector(None, b"isLessThanOrEqualTo:", b"Z@:@", isRequired=False),
        ],
    ),
    "NSDeprecatedKeyValueCoding": objc.informal_protocol(
        "NSDeprecatedKeyValueCoding",
        [
            objc.selector(None, b"valuesForKeys:", b"@@:@", isRequired=False),
            objc.selector(None, b"takeStoredValue:forKey:", b"v@:@@", isRequired=False),
            objc.selector(None, b"takeValue:forKey:", b"v@:@@", isRequired=False),
            objc.selector(None, b"storedValueForKey:", b"@@:@", isRequired=False),
            objc.selector(
                None, b"handleTakeValue:forUnboundKey:", b"v@:@@", isRequired=False
            ),
            objc.selector(None, b"useStoredAccessor", b"Z@:", isRequired=False),
            objc.selector(
                None, b"takeValuesFromDictionary:", b"v@:@", isRequired=False
            ),
            objc.selector(
                None, b"handleQueryWithUnboundKey:", b"@@:@", isRequired=False
            ),
            objc.selector(None, b"takeValue:forKeyPath:", b"v@:@@", isRequired=False),
            objc.selector(None, b"unableToSetNilForKey:", b"v@:@", isRequired=False),
        ],
    ),
    "NSScripting": objc.informal_protocol(
        "NSScripting",
        [
            objc.selector(None, b"setScriptingProperties:", b"v@:@", isRequired=False),
            objc.selector(
                None, b"scriptingValueForSpecifier:", b"@@:@", isRequired=False
            ),
            objc.selector(
                None,
                b"newScriptingObjectOfClass:forValueForKey:withContentsValue:properties:",
                b"@@:#@@@",
                isRequired=False,
            ),
            objc.selector(None, b"scriptingProperties", b"@@:", isRequired=False),
            objc.selector(
                None,
                b"copyScriptingValue:forKey:withProperties:",
                b"@@:@@@",
                isRequired=False,
            ),
        ],
    ),
    "NSKeyValueObserving": objc.informal_protocol(
        "NSKeyValueObserving",
        [
            objc.selector(
                None,
                b"observeValueForKeyPath:ofObject:change:context:",
                b"v@:@@@^v",
                isRequired=False,
            )
        ],
    ),
    "NSArchiverCallback": objc.informal_protocol(
        "NSArchiverCallback",
        [
            objc.selector(
                None, b"replacementObjectForArchiver:", b"@@:@", isRequired=False
            ),
            objc.selector(None, b"classForArchiver", b"#@:", isRequired=False),
        ],
    ),
    "NSThreadPerformAdditions": objc.informal_protocol(
        "NSThreadPerformAdditions",
        [
            objc.selector(
                None,
                b"performSelector:onThread:withObject:waitUntilDone:",
                b"v@::@@Z",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"performSelectorOnMainThread:withObject:waitUntilDone:",
                b"v@::@Z",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"performSelectorInBackground:withObject:",
                b"v@::@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"performSelector:onThread:withObject:waitUntilDone:modes:",
                b"v@::@@Z@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"performSelectorOnMainThread:withObject:waitUntilDone:modes:",
                b"v@::@Z@",
                isRequired=False,
            ),
        ],
    ),
    "NSKeyedUnarchiverObjectSubstitution": objc.informal_protocol(
        "NSKeyedUnarchiverObjectSubstitution",
        [objc.selector(None, b"classForKeyedUnarchiver", b"#@:", isRequired=False)],
    ),
    "NSScriptingComparisonMethods": objc.informal_protocol(
        "NSScriptingComparisonMethods",
        [
            objc.selector(None, b"scriptingContains:", b"Z@:@", isRequired=False),
            objc.selector(None, b"scriptingIsGreaterThan:", b"Z@:@", isRequired=False),
            objc.selector(None, b"scriptingEndsWith:", b"Z@:@", isRequired=False),
            objc.selector(None, b"scriptingIsLessThan:", b"Z@:@", isRequired=False),
            objc.selector(None, b"scriptingBeginsWith:", b"Z@:@", isRequired=False),
            objc.selector(
                None, b"scriptingIsGreaterThanOrEqualTo:", b"Z@:@", isRequired=False
            ),
            objc.selector(None, b"scriptingIsEqualTo:", b"Z@:@", isRequired=False),
            objc.selector(
                None, b"scriptingIsLessThanOrEqualTo:", b"Z@:@", isRequired=False
            ),
        ],
    ),
    "NSDistributedObjects": objc.informal_protocol(
        "NSDistributedObjects",
        [
            objc.selector(
                None, b"replacementObjectForPortCoder:", b"@@:@", isRequired=False
            ),
            objc.selector(None, b"classForPortCoder", b"#@:", isRequired=False),
        ],
    ),
    "NSKeyValueObserverRegistration": objc.informal_protocol(
        "NSKeyValueObserverRegistration",
        [
            objc.selector(
                None,
                b"removeObserver:forKeyPath:context:",
                b"v@:@@^v",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"addObserver:forKeyPath:options:context:",
                b"v@:@@Q^v",
                isRequired=False,
            ),
            objc.selector(
                None, b"removeObserver:forKeyPath:", b"v@:@@", isRequired=False
            ),
        ],
    ),
    "NSScriptObjectSpecifiers": objc.informal_protocol(
        "NSScriptObjectSpecifiers",
        [
            objc.selector(None, b"objectSpecifier", b"@@:", isRequired=False),
            objc.selector(
                None,
                b"indicesOfObjectsByEvaluatingObjectSpecifier:",
                b"@@:@",
                isRequired=False,
            ),
        ],
    ),
    "NSErrorRecoveryAttempting": objc.informal_protocol(
        "NSErrorRecoveryAttempting",
        [
            objc.selector(
                None,
                b"attemptRecoveryFromError:optionIndex:delegate:didRecoverSelector:contextInfo:",
                b"v@:@Q@:^v",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"attemptRecoveryFromError:optionIndex:",
                b"Z@:@Q",
                isRequired=False,
            ),
        ],
    ),
    "NSClassDescriptionPrimitives": objc.informal_protocol(
        "NSClassDescriptionPrimitives",
        [
            objc.selector(
                None, b"inverseForRelationshipKey:", b"@@:@", isRequired=False
            ),
            objc.selector(None, b"attributeKeys", b"@@:", isRequired=False),
            objc.selector(None, b"toOneRelationshipKeys", b"@@:", isRequired=False),
            objc.selector(None, b"classDescription", b"@@:", isRequired=False),
            objc.selector(None, b"toManyRelationshipKeys", b"@@:", isRequired=False),
        ],
    ),
    "NSURLClient": objc.informal_protocol(
        "NSURLClient",
        [
            objc.selector(
                None, b"URLResourceDidFinishLoading:", b"v@:@", isRequired=False
            ),
            objc.selector(
                None, b"URLResourceDidCancelLoading:", b"v@:@", isRequired=False
            ),
            objc.selector(
                None, b"URL:resourceDataDidBecomeAvailable:", b"v@:@@", isRequired=False
            ),
            objc.selector(
                None,
                b"URL:resourceDidFailLoadingWithReason:",
                b"v@:@@",
                isRequired=False,
            ),
        ],
    ),
    "NSKeyValueObservingCustomization": objc.informal_protocol(
        "NSKeyValueObservingCustomization",
        [
            objc.selector(None, b"observationInfo", b"^v@:", isRequired=False),
            objc.selector(None, b"setObservationInfo:", b"v@:^v", isRequired=False),
            objc.selector(
                None,
                b"keyPathsForValuesAffectingValueForKey:",
                b"@@:@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"automaticallyNotifiesObserversForKey:",
                b"Z@:@",
                isRequired=False,
            ),
        ],
    ),
    "NSDelayedPerforming": objc.informal_protocol(
        "NSDelayedPerforming",
        [
            objc.selector(
                None,
                b"performSelector:withObject:afterDelay:",
                b"v@::@d",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"cancelPreviousPerformRequestsWithTarget:",
                b"v@:@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"cancelPreviousPerformRequestsWithTarget:selector:object:",
                b"v@:@:@",
                isRequired=False,
            ),
            objc.selector(
                None,
                b"performSelector:withObject:afterDelay:inModes:",
                b"v@::@d@",
                isRequired=False,
            ),
        ],
    ),
    "NSKeyedArchiverObjectSubstitution": objc.informal_protocol(
        "NSKeyedArchiverObjectSubstitution",
        [
            objc.selector(
                None, b"replacementObjectForKeyedArchiver:", b"@@:@", isRequired=False
            ),
            objc.selector(None, b"classForKeyedArchiver", b"#@:", isRequired=False),
            objc.selector(
                None, b"classFallbacksForKeyedArchiver", b"@@:", isRequired=False
            ),
        ],
    ),
}
expressions = {
    "NSAppleEventSendDefaultOptions": "NSAppleEventSendWaitForReply | NSAppleEventSendCanInteract"
}

# END OF FILE
